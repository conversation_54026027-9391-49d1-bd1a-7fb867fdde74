// server/server.js - Mock server based on activity.proto
import { fileURLToPath } from 'node:url';
import { dirname, join } from 'node:path';
import process from 'node:process';
import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import dayjs from 'dayjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = 3002;

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));

// --- Helper Functions for Mock Data Generation ---
function generateUserInfo(uid, _usernamePrefix = 'User') {
    return {
        uid,
        username: `tt110200509`,
        alias: `Alias_${uid}`,
        nickname: `Nickname ${uid}`,
        sex: uid % 2 === 0 ? 1 : 2, // 1 for male, 2 for female
        guildInfo: {
            name: `Guild_${String.fromCharCode(65 + (uid % 5))}`,
            guildId: 100 + (uid % 10),
            displayId: 1000 + (uid % 100),
        },
        role: uid % 3, // 0, 1, 2
    };
}

function generateChannelInfo(channelId) {
    const statusValues = [0, 1, 2, 3, 4]; // LEAVE, STAY, WATCH, LIVE, PK
    return {
        channelId,
        status: statusValues[channelId % statusValues.length],
    };
}

function generateCommonRankItem(uid, rank, value) {
    return {
        uid,
        rank,
        rankHuman: `${rank}`,
        value,
        valueHuman: `${value}收藏石`,
        ltPrevValue: value + 100,
        ltPrevValueHuman: `${value + 100}收藏石`,
        gtNextValue: value - 100,
        gtNextValueHuman: `${value - 100}收藏石`,
        userInfo: generateUserInfo(uid),
        channelInfo: generateChannelInfo(uid + 1000),
        mvpInfoList: [
            {
                rank: 1,
                value: Math.floor(Math.random() * 10000) + 1000,
                valueHuman: `${Math.floor(Math.random() * 10000) + 1000}豆`,
                userInfo: generateUserInfo(uid + 100),
            },
        ],
    };
}

// 验证请求参数
function validateRequest(reqBody, requiredFields = []) {
    const errors = [];

    if (!reqBody) {
        errors.push('Request body is required');
        return errors;
    }

    requiredFields.forEach((field) => {
        if (reqBody[field] === undefined || reqBody[field] === null) {
            errors.push(`Field '${field}' is required`);
        }
    });

    return errors;
}

// --- Mock Data Definitions ---
const mockResponses = {
    // 初始化接口 - 符合 InitResp 定义
    init: reqBody => ({
        serverTime: Math.floor(Date.now() / 1000),
        startTime: Math.floor(dayjs().subtract(7, 'day').valueOf() / 1000),
        endTime: Math.floor(dayjs().add(7, 'day').valueOf() / 1000),
        userInfo: generateUserInfo(reqBody.uid || 123456),
    }),

    // 传奇加冕馆-获取用户任务 - 符合 GetUserTaskResp 定义
    getUserTask: reqBody => ({
        taskValue: (Math.floor(Math.random() * 100) + 50) * 10000,
        userInfo: generateUserInfo(reqBody.uid || 123456),
        cards: [
            { cardId: 'card_1', name: '紫金神话', time: Math.floor(Date.now() / 1000) - 86400 },
            { cardId: 'card_2', name: '橙金神话', time: Math.floor(Date.now() / 1000) - 43200 },
            { cardId: 'card_3', name: '纯金传说', time: Math.floor(Date.now() / 1000) },
        ],
    }),

    // 惊喜秘宝阁-活动用户卷轴 - 符合 getUserScrollResp 定义
    getUserScroll: _reqBody => ({
        tickValue: Math.floor(Math.random() * 10) + 1,
        list: [
            { tickId: 'tick_1', sendValue: Math.floor(Math.random() * 1000) + 100, isFixCard: false },
            { tickId: 'tick_2', sendValue: Math.floor(Math.random() * 1000) + 100, isFixCard: true },
            { tickId: 'tick_3', sendValue: Math.floor(Math.random() * 1000) + 100, isFixCard: false },
        ],
    }),

    // 惊喜秘宝阁-开启卷轴 - 符合 OpenTickResp 定义
    openTick: _reqBody => ({
        id: `A${Math.floor(Math.random() * 10) + 1}`,
        num: Math.floor(Math.random() * 5) + 1,
        time: Math.floor(Date.now() / 1000),
    }),

    // 惊喜秘宝阁-补卡 - 符合 Empty 定义
    repairTick: _reqBody => ({}),

    // 惊喜秘宝阁-获取当天补卡信息 - 符合 GetRepairTickResp 定义
    getRepairTick: reqBody => ({
        uid: reqBody.uid || 123456,
        fixCardInfo: {
            tickId: 'tick_1',
            sendValue: Math.floor(Math.random() * 1000) + 500,
            fixCardDate: dayjs().format('YYYY-MM-DD'),
            startDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
            endDate: dayjs().add(1, 'day').format('YYYY-MM-DD'),
        },
    }),

    // 惊喜秘宝阁-奖励记录 - 符合 GetBaogeRewardRecordResp 定义
    getBaogeRewardRecord: (reqBody) => {
        const { page = 1, size = 10 } = reqBody;
        const totalItems = 50;
        const list = [];

        for (let i = 0; i < Math.max(size, 10); i++) {
            const itemIndex = (page - 1) * size + i;
            if (itemIndex >= totalItems)
                break;

            list.push({
                time: Math.floor(Date.now() / 1000) - itemIndex * 3600,
                rewards: [
                    { id: `A${(i % 5) + 1}`, num: (i % 3) + 1, time: Math.floor(Date.now() / 1000) },
                    { id: `A${(i % 3) + 6}`, num: (i % 2) + 1, time: Math.floor(Date.now() / 1000) },
                ],
            });
        }

        return { list, total: totalItems };
    },

    // 黑金收藏厅-获取用户赠礼列表 - 符合 GetUserGiftResp 定义
    getUserGift: _reqBody => ({
        kingGiftList: [
            { id: 'A1', num: Math.floor(Math.random() * 10), onLineTime: Math.floor(Date.now() / 1000) - 86400, offLineTime: Math.floor(Date.now() / 1000) + 86400 },
            { id: 'A2', num: Math.floor(Math.random() * 10), onLineTime: Math.floor(Date.now() / 1000) - 43200, offLineTime: Math.floor(Date.now() / 1000) + 43200 },
            { id: 'A3', num: Math.floor(Math.random() * 10), onLineTime: Math.floor(Date.now() / 1000) - 21600, offLineTime: Math.floor(Date.now() / 1000) + 21600 },
            { id: 'A4', num: Math.floor(Math.random() * 10), onLineTime: Math.floor(Date.now() / 1000) - 10800, offLineTime: Math.floor(Date.now() / 1000) + 10800 },
        ],
        goodGiftList: [
            { id: 'A5', num: Math.floor(Math.random() * 15), onLineTime: Math.floor(Date.now() / 1000) - 86400, offLineTime: Math.floor(Date.now() / 1000) + 86400 },
            { id: 'A6', num: Math.floor(Math.random() * 15), onLineTime: Math.floor(Date.now() / 1000) - 43200, offLineTime: Math.floor(Date.now() / 1000) + 43200 },
            { id: 'A7', num: Math.floor(Math.random() * 15), onLineTime: Math.floor(Date.now() / 1000) - 21600, offLineTime: Math.floor(Date.now() / 1000) + 21600 },
            { id: 'A8', num: Math.floor(Math.random() * 15), onLineTime: Math.floor(Date.now() / 1000) - 10800, offLineTime: Math.floor(Date.now() / 1000) + 10800 },
        ],
    }),

    // 黑金收藏厅-获取高光印记 - 符合 GetHighlightMarkResp 定义
    getHighlightMark: (reqBody) => {
        const { page = 1, size = 10 } = reqBody;
        const totalItems = 100;
        const list = [];

        for (let i = 0; i < Math.min(size, 10); i++) {
            const itemIndex = (page - 1) * size + i;
            if (itemIndex >= totalItems)
                break;

            list.push({
                id: `A${(i % 8) + 1}`,
                fromUserInfo: generateUserInfo(123456 + i),
                toUserInfo: generateUserInfo(654321 + i),
                time: Math.floor(Date.now() / 1000) - itemIndex * 1800,
            });
        }

        return { total: totalItems, list };
    },

    // 黑金收藏厅-榜单 - 符合 GetBlackGoldRankResp 定义
    getBlackGoldRank: (reqBody) => {
        const { page = 1, size = 20, uid } = reqBody;
        const totalItems = 100;
        const list = [];

        for (let i = 0; i < Math.max(size, 10); i++) {
            const rank = (page - 1) * size + i + 1;
            if (rank > totalItems)
                break;

            const userUid = (uid || 123456) + i + 100;
            const value = 100000 - rank * 1000;
            list.push(generateCommonRankItem(userUid, rank, value));
        }

        return {
            total: totalItems,
            list,
            self: uid ? generateCommonRankItem(uid, 15, 85000) : null,
        };
    },

    // 至尊神壕榜-榜单 - 符合 GetSupremeRankResp 定义
    getSupremeRank: (reqBody) => {
        const { page = 1, size = 20, uid, _date } = reqBody;
        const totalItems = 200;
        const list = [];

        for (let i = 0; i < Math.max(size, 10); i++) {
            const rank = (page - 1) * size + i + 1;
            if (rank > totalItems)
                break;

            const userUid = (uid || 123456) + i + 200;
            const value = 500000 - rank * 5000;
            list.push(generateCommonRankItem(userUid, rank, value));
        }

        return {
            total: totalItems,
            list,
            self: uid ? generateCommonRankItem(uid, 25, 375000) : null,
        };
    },

};

// --- API Route Definitions ---
// 严格按照 activity.proto 中的 Activity 服务定义
const apiEndpoints = {
    // 初始化
    init: { method: 'post', reqType: 'InitReq', respType: 'init' },
    // 传奇加冕馆-获取用户任务
    getUserTask: { method: 'post', reqType: 'InitReq', respType: 'getUserTask' },
    // 惊喜秘宝阁-活动用户卷轴
    getUserScroll: { method: 'post', reqType: 'InitReq', respType: 'getUserScroll' },
    // 惊喜秘宝阁-开启卷轴
    openTick: { method: 'post', reqType: 'InitReq', respType: 'openTick' },
    // 惊喜秘宝阁-补卡
    repairTick: { method: 'post', reqType: 'RepairTickReq', respType: 'repairTick' },
    // 惊喜秘宝阁-获取当天补卡信息
    getRepairTick: { method: 'post', reqType: 'GetRepairTickReq', respType: 'getRepairTick' },
    // 惊喜秘宝阁-奖励记录
    getBaogeRewardRecord: { method: 'post', reqType: 'GetBaogeRewardRecordReq', respType: 'getBaogeRewardRecord' },
    // 黑金收藏厅-获取用户赠礼列表
    getUserGift: { method: 'post', reqType: 'InitReq', respType: 'getUserGift' },
    // 黑金收藏厅-获取高光印记
    getHighlightMark: { method: 'post', reqType: 'GetHighlightMarkReq', respType: 'getHighlightMark' },
    // 黑金收藏厅-榜单
    getBlackGoldRank: { method: 'post', reqType: 'GetBlackGoldRankReq', respType: 'getBlackGoldRank' },
    // 至尊神壕榜-榜单
    getSupremeRank: { method: 'post', reqType: 'GetSupremeRankReq', respType: 'getSupremeRank' },
};

// 请求参数验证规则
const validationRules = {
    InitReq: ['uid'],
    RepairTickReq: ['uid', 'tickId'],
    GetRepairTickReq: ['uid'],
    GetBaogeRewardRecordReq: ['uid'],
    GetHighlightMarkReq: [],
    GetBlackGoldRankReq: ['uid'],
    GetSupremeRankReq: ['uid'],
};

Object.entries(apiEndpoints).forEach(([rpcName, config]) => {
    const routePath = `/activity.Activity/${rpcName}`;
    const responseGenerator = mockResponses[config.respType];

    if (!responseGenerator) {
        console.error(`No response generator found for ${config.respType} (RPC: ${rpcName})`);
        return;
    }

    if (config.method === 'get') {
        app.get(routePath, (req, res) => {
            try {
                if (process.env.NODE_ENV === 'development') {
                    console.log(`GET ${routePath}`);
                }

                const mockResponseData = responseGenerator(req.query);
                res.json({
                    code: 0,
                    msg: 'success',
                    data: mockResponseData,
                });
            }
            catch (error) {
                console.error(`Error in GET ${routePath}:`, error);
                res.status(500).json({
                    code: -1,
                    msg: 'Internal server error',
                    data: null,
                });
            }
        });
    }
    else if (config.method === 'post') {
        app.post(routePath, (req, res) => {
            try {
                if (process.env.NODE_ENV === 'development') {
                    console.log(`POST ${routePath} with body:`, req.body);
                }

                // 验证请求参数
                const requiredFields = validationRules[config.reqType] || [];
                const validationErrors = validateRequest(req.body, requiredFields);

                if (validationErrors.length > 0) {
                    return res.status(400).json({
                        code: 400,
                        msg: `Validation failed: ${validationErrors.join(', ')}`,
                        data: null,
                    });
                }

                const mockResponseData = responseGenerator(req.body);
                res.json({
                    code: 0,
                    msg: 'success',
                    data: mockResponseData,
                });
            }
            catch (error) {
                console.error(`Error in POST ${routePath}:`, error);
                res.status(500).json({
                    code: -1,
                    msg: 'Internal server error',
                    data: null,
                });
            }
        });
    }
});

// Serve static files (if any)
app.use(express.static(join(__dirname, 'public')));

// Start server
app.listen(PORT, () => {
    console.log(`Mock server is running on http://localhost:${PORT}`);
    console.log('Available RPC endpoints:');
    Object.entries(apiEndpoints).forEach(([rpcName, config]) => {
        console.log(`- ${config.method.toUpperCase()} /activity.Activity/${rpcName}`);
    });
});

export default app;
