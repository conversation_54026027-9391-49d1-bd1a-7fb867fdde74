
syntax = "proto3";

package activity;

service Activity {
  // 初始化
  rpc init(InitReq) returns (InitResp) {};
  // 传奇加冕馆-获取用户任务
  rpc getUserTask(InitReq) returns (GetUserTaskResp) {};
  // 惊喜秘宝阁-活动用户卷轴
  rpc getUserScroll(InitReq) returns (getUserScrollResp) {};
  // 惊喜秘宝阁-开启卷轴
  rpc openTick(InitReq) returns (OpenTickResp) {};
  // 惊喜秘宝阁-补卡
  rpc repairTick(RepairTickReq) returns (Empty) {};
  // 惊喜秘宝阁-获取当天补卡信息
  rpc getRepairTick(GetRepairTickReq) returns (GetRepairTickResp) {};
  // 惊喜秘宝阁-奖励记录
  rpc getBaogeRewardRecord(GetBaogeRewardRecordReq) returns (GetBaogeRewardRecordResp) {};
  // 黑金收藏厅-获取用户赠礼列表
  rpc getUserGift(InitReq) returns (GetUserGiftResp) {};
  // 黑金收藏厅-获取高光印记
  rpc getHighlightMark(GetHighlightMarkReq) returns (GetHighlightMarkResp) {};
  // 黑金收藏厅-榜单
  rpc getBlackGoldRank(GetBlackGoldRankReq) returns (GetBlackGoldRankResp) {};
  // 至尊神壕榜-榜单
  rpc getSupremeRank(GetSupremeRankReq) returns (GetSupremeRankResp) {};
}

message Empty {}

message InitReq {
  uint32 uid = 1;
}

message InitResp {
  uint32 serverTime = 1; // 服务器时间
  uint32 startTime = 2; // 活动开始时间
  uint32 endTime = 3; // 活动结束时间
  UserInfo userInfo = 4; //用户信息
}


/********** types **********/

message GuildInfo {
  string name = 1;
  uint32 guildId = 2; // 一般不外显
  uint32 displayId = 3;
}

message UserInfo {
  uint32 uid = 1;
  string username = 2;
  string alias = 3;
  string nickname = 4;
  uint32 sex = 5; // 非1为女
  optional GuildInfo guildInfo = 6; // 公会信息
  optional uint32 role = 7; // 用户角色，具体值由业务决定
}

enum ChannelStatus {
  LEAVE = 0; // 不在房
  STAY  = 1; // 在房
  WATCH = 2; // 看直播
  LIVE  = 3; // 直播中
  PK    = 4; // PK
}

message ChannelInfo {
  uint32 channelId = 1;
  ChannelStatus status = 2; // 在房状态
}

message MvpInfo {
  uint32 rank = 1;
  uint32 value = 2;
  string valueHuman = 3;
  UserInfo userInfo = 4;
}

message CommonRankItem {
  uint32 uid = 1;
  uint32 rank = 2;
  string rankHuman = 3;
  uint32 value = 4;
  string valueHuman = 5;
  uint32 ltPrevValue = 6;
  string ltPrevValueHuman = 7;
  uint32 gtNextValue = 8;
  string gtNextValueHuman = 9;
  UserInfo userInfo = 10;
  optional ChannelInfo channelInfo = 11; // 可选
  repeated MvpInfo mvpInfoList = 12; // 可选
}

message GetUserTaskResp {
  uint32 taskValue = 1; // 用户累计任务完成值
  UserInfo userInfo = 2;
  repeated Item cards = 3;
  message Item {
    string cardId = 1; // 身份卡牌id. (card_1 ~ card_6)
    string name = 2;
    uint32 time = 3; // 解锁时间
  }
}

message getUserScrollResp {
  uint32 tickValue = 1; // 可开启卷轴次数
  repeated Item list = 2;
  message Item {
    string tickId = 1; // 卷轴id (tick_1 ~ tick_7)
    uint32 sendValue = 2; // 送礼值
    optional bool isFixCard = 3 [ default = false ]; // 是否补卡中
  }
}

message OpenTickResp {
  optional string id = 1;
  optional uint32 num = 2;
  optional uint32 time = 3;
}

message RepairTickReq {
  uint32 uid = 1;
  string tickId = 2; // 补卡卷轴id. (tick_1 ~ tick_7)
}

message GetRepairTickReq {
  uint32 uid = 1;
}
message GetRepairTickResp {
  uint32 uid = 1;
  Item fixCardInfo = 2; // 补卡卷轴
  message Item {
    string tickId = 1; // 卷轴id (tick_1 ~ tick_7)
    uint32 sendValue = 2; // 送礼值
    string fixCardDate = 3; // 补卡日期
    string startDate = 4; // 开始日期
    string endDate = 5; // 结束日期
  }
}

message GetUserGiftResp {
  repeated Item kingGiftList = 1; // 帝王套礼物
  repeated Item goodGiftList = 2; // 臻品礼物
  message Item {
    string id = 1;
    uint32 num = 2;
    optional uint32 onLineTime = 3; // 上线时间
    optional uint32 offLineTime = 4; // 下线时间
  }
}


message GetHighlightMarkReq {
  uint32 page = 1 [ default = 1 ];
  uint32 size = 2 [ default = 10 ];
}
message GetHighlightMarkResp {
  uint32 total = 1;
  repeated Item list = 2;
  message Item { 
    string id = 1; // 礼物id
    UserInfo fromUserInfo = 2; // 送礼人
    UserInfo toUserInfo = 3; // 接礼人
    uint32 time = 4; // 赠送时间
  }
}

message GetBlackGoldRankReq {
  uint32 uid = 1;
  uint32 page = 2 [ default = 1 ];
  uint32 size = 3 [ default = 10 ];
}
message GetBlackGoldRankResp {
  uint32 total = 1;       // 总数
  CommonRankItem self = 2;    // 底部我的排名信息
  repeated CommonRankItem list = 3; // 列表信息
}

message GetSupremeRankReq {
  uint32 uid = 1;
  uint32 page = 2 [ default = 1 ];
  uint32 size = 3 [ default = 10 ];
  optional string date = 4; // 榜单日期 YYYYMMDD 20230501 不传默认为总榜
}
message GetSupremeRankResp {
  uint32 total = 1;       // 总数
  CommonRankItem self = 2;    // 底部我的排名信息
  repeated CommonRankItem list = 3; // 列表信息
}

message GetBaogeRewardRecordReq {
  uint32 uid = 1;
  uint32 page = 2 [ default = 1 ];
  uint32 size = 3 [ default = 10 ];
}
message GetBaogeRewardRecordResp {
  repeated Item list = 1;
  uint32 total = 2;
  message Item { 
    uint32 time = 1;  // 时间
    repeated Reward rewards = 3; // 奖励
    message Reward {
      string id = 1; // 资源ID
      optional uint32 num = 2;
      optional uint32 time = 3;
    }
  }
}
