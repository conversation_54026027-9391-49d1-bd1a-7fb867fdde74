import { URL, fileURLToPath } from 'node:url';
import { defineConfig } from 'vite';
import autoprefixer from 'autoprefixer';
import viewport from 'postcss-mobile-forever';
import { createVitePlugins } from './build/vite';

// https://vitejs.dev/config/
export default defineConfig({
    base: './',
    server: {
        host: '0.0.0.0',
        port: 8888,
    },
    watch: {
        ignored: ['!**/node_modules/@vant/**'],
        usePolling: true,
    },
    preview: {
        host: '0.0.0.0',
        port: 4173,
    },
    plugins: createVitePlugins(),
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url)),
        },
    },
    css: {
        preprocessorOptions: {
            less: {
                charset: false,
                additionalData: '@import "@/styles/varsbank.less";',
            },
        },
        postcss: {
            plugins: [
                autoprefixer(),
                // https://github.com/wswmsword/postcss-mobile-forever
                viewport({
                    appSelector: '#app',
                    // 区分挂件和活动页的设计稿宽度
                    viewportWidth: file =>
                        file.includes(fileURLToPath(new URL('./src/pendant/', import.meta.url)))
                            ? 165
                            : 375,
                    // 页面显示最大宽度，简单适配PC端
                    // maxDisplayWidth: 600,
                    // 宽度设置为100%的根元素组件，防止溢出#app元素
                    rootContainingBlockSelectorList: ['van-tabbar', 'van-popup'],
                    // 选择器黑名单，名单上的不转换
                    selectorBlackList: ['van-overlay'],
                }),
            ],
        },
    },
    build: {
        // 不转换静态资源为 base64
        assetsInlineLimit: 0,
        rollupOptions: {
            input: {
                main: fileURLToPath(new URL('./index.html', import.meta.url)),
                pendant: fileURLToPath(new URL('./pendant.html', import.meta.url)),
            },
            output: {
                chunkFileNames: 'static/js/[name]-[hash].js',
                entryFileNames: 'static/js/[name]-[hash].js',
                // 产物归类
                assetFileNames: ({ name }) => {
                    switch (true) {
                        // css文件
                        case /\.css(?:\?.*)?$/.test(name):
                            return 'static/css/[name]-[hash][extname]';
                        // 图片文件
                        case /\.(?:png|jpe?g|gif|svg)(?:\?.*)?$/.test(name):
                            return 'static/img/[name]-[hash][extname]';
                        // 字体文件
                        case /\.(?:woff2?|eot|ttf|otf)(?:\?.*)?$/i.test(name):
                            return 'static/fonts/[name]-[hash][extname]';
                        // 媒体文件
                        case /\.(?:mp4|webm|ogg|mp3|wav|flac|aac)(?:\?.*)?$/i.test(name):
                            return 'static/media/[name]-[hash][extname]';
                        // js依赖
                        case /\.js(?:\?.*)?$/i.test(name):
                            return 'static/js/[name]-[hash][extname]';
                        default:
                            return 'static/[name]-[hash][extname]';
                    }
                },
            },
        },
    },
});
