<route lang="json">
{
    "name": "home",
    "path": "/:pathMatch(.*)*",
    "meta": {
        "title": "浮光圣殿 鎏金之夜"
    }
}
</route>

<template>
    <page-container>
        <div
            id="home-content"
            ref="wrapper"
            class="home"
            @scroll="handleInfinite"
        >
            <PageTopBar
                v-if="showStatusBar"
                class="absolute left-0 top-0 z-[99999999] m-auto"
                color="#2f261e"
            ></PageTopBar>
            <div class="banner relative">
                <home-tab class="absolute top-[250px] z-[100]" />
                <img
                    class="absolute left-[0] top-99 z-100 h-[29.5px] w-[62.5px]"
                    :src="requireImg('<EMAIL>')"
                    @click="jumpToLink(RULE_LINK)"
                >

                <img
                    class="absolute left-[0] top-[139px] z-100 h-[29.5px] w-[50px]"
                    :src="requireImg('<EMAIL>')"
                    @click="jumpToLink(RULE_LINK)"
                >
                <img
                    class="breath absolute right-[0] top-[101px] z-100 h-[65.5px] w-[71.5px]"
                    :src="requireImg('<EMAIL>')"
                    @click="useEventBus('rank-popup').emit({ show: true })"
                >
            </div>
            <div
                v-if="initStore.serverTime"
                class="bg-content mt-[59px]"
            >
                <tab1 v-if="current === 1"></tab1>
                <tab2 v-if="current === 2"></tab2>
                <tab3 v-if="current === 3"></tab3>
                <div
                    v-if="myWebview.isIOS()"
                    class="mt-[10px] w-full flex items-center justify-center text-[12px] text-[#E4C59D]"
                >
                    本活动与苹果公司无关
                </div>
            </div>
            <draw-record-modal></draw-record-modal>
            <cp-reward-popup></cp-reward-popup>
            <cp-list-popup></cp-list-popup>
            <hint-modal></hint-modal>
            <rank-popup></rank-popup>
            <confirm-draw-modal></confirm-draw-modal>
            <task-modal></task-modal>
            <modal-cp-card></modal-cp-card>
            <dating-card-modal></dating-card-modal>
            <dragBtn v-if="current === 1">
            </dragBtn>
        </div>
    </page-container>
</template>

<script setup name="Home">
import { throttle } from 'lodash-es';
import useInitStore from '@/stores/modules/use-init-store';
import useDrawStore from '@/components/tab3/use-draw';
import { pageView, track } from '@/utils/jsbridge';

import useNav from '@/components/nav/use-nav';

const showStatusBar = computed(() => myWebview.params.immersion && (myWebview.isInApp() || myWebview.params.isDev));

const navStore = useNav();
const { current } = storeToRefs(navStore);
const drawStore = useDrawStore();

const wrapper = ref();

const router = useRouter();
const initStore = useInitStore();

onMounted(async () => {
    const { sourceform } = myWebview.params;
    pageView('activity_page', sourceform || 0);
    const toast = showLoading();
    await initStore.init({ flag: true });
    toast.close();
});

// 将事件处理函数抽离为独立的 composable
const useScrollHandler = () => {
    const handleInfinite = throttle(() => {
        const scrollTop = wrapper.value?.scrollTop;
        if (scrollTop + wrapper.value.clientHeight >= wrapper.value.scrollHeight - 30) {
            useEventBus('cp-rank').emit();
        }
    }, 1000);

    return { handleInfinite };
};
const { handleInfinite } = useScrollHandler();
</script>

<style lang="less" scoped>
.home {
    overflow: auto;
    background: #2d0607;
    width: 100vw;
    min-height: 100vh;
    height: 100vh;

    .banner {
        width: 374px;
        height: 258px;
        background-image: url('@/assets/img/<EMAIL>');
        background-size: 100% 100%;
    }

    .bg-content {
        width: 100%;
        // background: #5adbf5;
        background-size: 100% auto;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
}
</style>
