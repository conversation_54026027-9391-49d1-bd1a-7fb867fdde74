export const rewardList = [
    {
        resource_id: 'card_1',
        name: '浮光新贵',
        type: 'other',
        mark: '成就卡',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'card_1.png',
    },
    {
        resource_id: 'card_2',
        name: '蔚蓝尊者',
        type: 'other',
        mark: '成就卡',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'card_2.png',
    },
    {
        resource_id: 'card_3',
        name: '紫曜尊使',
        type: 'other',
        mark: '成就卡',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'card_3.png',
    },
    {
        resource_id: 'card_4',
        name: '赤金名爵',
        type: 'other',
        mark: '成就卡',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'card_4.png',
    },
    {
        resource_id: 'card_5',
        name: '幻金圣尊',
        type: 'other',
        mark: '成就卡',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'card_5.png',
    },
    {
        resource_id: 'card_6',
        name: '传奇神尊',
        type: 'other',
        mark: '成就卡',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'card_6.png',
    },
    {
        resource_id: 'BDG_lv1',
        name: '完美人格',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'BDG_lv1.png',
    },
    {
        resource_id: 'BDG_lv2',
        name: '天下萌主',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'BDG_lv2.png',
    },
    {
        resource_id: 'BDG_lv3',
        name: '迎财纳福',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'BDG_lv3.png',
    },
    {
        resource_id: 'BDG_lv4',
        name: '浪漫超标',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'BDG_lv4.png',
    },
    {
        resource_id: 'BDG_lv5',
        name: '甜度过载',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'BDG_lv5.png',
    },
    {
        resource_id: 'BDG_lv6',
        name: '个_直播4月中春季赛1519',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'BDG_lv6.png',
    },
    {
        resource_id: 'BDG_X',
        name: '个_电竞推广1541',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆（定制）',
        unit: '天',
        special_type: '',
        image: 'BDG_X.png',
    },
    {
        resource_id: 'ENT_lv1',
        name: '男小猪',
        type: 'mount',
        mark: '坐骑',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_lv1.png',
    },
    {
        resource_id: 'ENT_lv2',
        name: '巴士',
        type: 'mount',
        mark: '坐骑',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_lv2.png',
    },
    {
        resource_id: 'ENT_lv3',
        name: '787露营温泉',
        type: 'mount',
        mark: '坐骑',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_lv3.png',
    },
    {
        resource_id: 'ENT_lv4',
        name: '男小猪',
        type: 'mount',
        mark: '坐骑',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_lv4.png',
    },
    {
        resource_id: 'ENT_lv5',
        name: '巴士',
        type: 'mount',
        mark: '坐骑',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_lv5.png',
    },
    {
        resource_id: 'ENT_lv6',
        name: '787露营温泉',
        type: 'mount',
        mark: '坐骑',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_lv6.png',
    },
    {
        resource_id: 'HD_lv1',
        name: '0.1元lv2冰沙',
        type: 'headwear',
        mark: '麦位框',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'HD_lv1.png',
    },
    {
        resource_id: 'HD_lv2',
        name: '0.1元lv2葡萄',
        type: 'headwear',
        mark: '麦位框',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'HD_lv2.png',
    },
    {
        resource_id: 'HD_lv3',
        name: '0.1元lv2咖啡',
        type: 'headwear',
        mark: '麦位框',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'HD_lv3.png',
    },
    {
        resource_id: 'HD_lv4',
        name: '25年lv3愚人节',
        type: 'headwear',
        mark: '麦位框',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'HD_lv4.png',
    },
    {
        resource_id: 'HD_lv5',
        name: '春日舞曲1302',
        type: 'headwear',
        mark: '麦位框',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'HD_lv5.png',
    },
    {
        resource_id: 'HD_lv6',
        name: '春日舞曲1300',
        type: 'headwear',
        mark: '麦位框',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'HD_lv6.png',
    },
    {
        resource_id: 'VR_1',
        name: '福赠金榜送礼总榜前10',
        type: 'official_cert',
        mark: '大V认证',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_1.png',
    },
    {
        resource_id: 'VR_2',
        name: '福赠金榜送礼总榜前20',
        type: 'official_cert',
        mark: '大V认证',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_2.png',
    },
    {
        resource_id: 'PWD_lv1',
        name: '捕猎大丰收',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '传奇加冕馆',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_lv1.png',
    },
    {
        resource_id: 'PWD_lv2',
        name: '蔚蓝尊者现世',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '传奇加冕馆',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_lv2.png',
    },
    {
        resource_id: 'PWD_lv3',
        name: '弹_lv2海螺1499',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '传奇加冕馆',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_lv3.png',
    },
    {
        resource_id: 'PWD_lv4',
        name: '大R会场赤金名爵',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '传奇加冕馆',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_lv4.png',
    },
    {
        resource_id: 'PWD_lv5',
        name: '捕猎大丰收',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '传奇加冕馆',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_lv5.png',
    },
    {
        resource_id: 'PWD_lv6',
        name: '捕获复制器',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '传奇加冕馆',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_lv6.png',
    },
    {
        resource_id: 'AMCN_lv1',
        name: '传奇加冕馆浮光新贵',
        type: 'breaking_news',
        mark: '全服公告',
        module: '传奇加冕馆',
        unit: '条',
        special_type: '',
        image: 'AMCN_lv1.png',
    },
    {
        resource_id: 'AMCN_lv2',
        name: '传奇加冕馆蔚蓝尊者',
        type: 'breaking_news',
        mark: '全服公告',
        module: '传奇加冕馆',
        unit: '条',
        special_type: '',
        image: 'AMCN_lv2.png',
    },
    {
        resource_id: 'AMCN_lv3',
        name: '传奇加冕馆紫耀尊使',
        type: 'breaking_news',
        mark: '全服公告',
        module: '传奇加冕馆',
        unit: '条',
        special_type: '',
        image: 'AMCN_lv3.png',
    },
    {
        resource_id: 'AMCN_lv4',
        name: '传奇加冕馆赤金名爵',
        type: 'breaking_news',
        mark: '全服公告',
        module: '传奇加冕馆',
        unit: '条',
        special_type: '',
        image: 'AMCN_lv4.png',
    },
    {
        resource_id: 'AMCN_lv5',
        name: '传奇加冕馆幻金圣尊',
        type: 'breaking_news',
        mark: '全服公告',
        module: '传奇加冕馆',
        unit: '条',
        special_type: '',
        image: 'AMCN_lv5.png',
    },
    {
        resource_id: 'AMCN_lv6',
        name: '传奇加冕馆传奇神尊',
        type: 'breaking_news',
        mark: '全服公告',
        module: '传奇加冕馆',
        unit: '条',
        special_type: '',
        image: 'AMCN_lv6.png',
    },
    {
        resource_id: 'gift_1314',
        name: '营收520',
        type: 'present_privilege',
        mark: '礼物赠送权',
        module: '传奇加冕馆',
        unit: '天',
        price: 52000,
        special_type: '常规礼物赠送权',
        image: 'gift_1314.png',
    },
    {
        resource_id: 'gift_3000',
        name: '壕友会1314',
        type: 'present_privilege',
        mark: '礼物赠送权',
        module: '传奇加冕馆',
        unit: '天',
        price: 131400,
        special_type: '常规礼物赠送权',
        image: 'gift_3000.png',
    },
    {
        resource_id: 'gift_5000',
        name: '礼_lyxyshd2226',
        type: 'present_privilege',
        mark: '礼物赠送权',
        module: '传奇加冕馆',
        unit: '天',
        price: 10000,
        special_type: '常规礼物赠送权',
        image: 'gift_5000.png',
    },
    {
        resource_id: 'gift_1w',
        name: '娱乐5月下100',
        type: 'present_privilege',
        mark: '礼物赠送权',
        module: '传奇加冕馆',
        unit: '天',
        price: 10000,
        special_type: '常规礼物赠送权',
        image: 'gift_1w.png',
    },
    {
        resource_id: 'ENT_C1',
        name: '坐_娱乐5月下1963',
        type: 'mount',
        mark: '坐骑',
        module: '惊喜秘宝阁',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_C1.png',
    },
    {
        resource_id: 'HD_C1',
        name: '25年lv3愚人节',
        type: 'headwear',
        mark: '麦位框',
        module: '惊喜秘宝阁',
        unit: '天',
        special_type: '自定义',
        image: 'HD_C1.png',
    },
    {
        resource_id: 'BDG_C1',
        name: '个_4月上贵族节1579',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '惊喜秘宝阁',
        unit: '天',
        special_type: '',
        image: 'BDG_C1.png',
    },
    {
        resource_id: 'PKG_C1',
        name: '甜蜜花海',
        type: 'package',
        mark: '包裹',
        module: '惊喜秘宝阁',
        unit: '个',
        price: 50000,
        special_type: '豆豆礼物',
        image: 'PKG_C1.png',
    },
    {
        resource_id: 'D1',
        name: '惊喜秘宝阁D1',
        type: 'breaking_news',
        mark: '全服公告',
        module: '惊喜秘宝阁',
        unit: '条',
        special_type: '',
        image: 'D1.png',
    },
    {
        resource_id: 'A1',
        name: '凤舞迎春',
        type: 'other',
        mark: '帝王套礼物',
        module: '黑金收藏厅',
        unit: '天',
        price: 999900,
        special_type: '礼物架豆豆礼物',
        image: 'A1.png',
    },
    {
        resource_id: 'A2',
        name: '鎏金华彩99',
        type: 'other',
        mark: '帝王套礼物',
        module: '黑金收藏厅',
        unit: '天',
        price: 1388000,
        special_type: '礼物架豆豆礼物',
        image: 'A2.png',
    },
    {
        resource_id: 'A3',
        name: '真爱帝王套',
        type: 'other',
        mark: '帝王套礼物',
        module: '黑金收藏厅',
        unit: '天',
        price: 1314520,
        special_type: '礼物架豆豆礼物',
        image: 'A3.png',
    },
    {
        resource_id: 'A4',
        name: '新帝王套',
        type: 'other',
        mark: '帝王套礼物',
        module: '黑金收藏厅',
        unit: '天',
        price: 888800,
        special_type: '礼物架豆豆礼物',
        image: 'A4.png',
    },
    {
        resource_id: 'A5',
        name: '水晶鞋',
        type: 'gift',
        mark: '礼物',
        module: '黑金收藏厅',
        unit: '个',
        price: 5000,
        special_type: '礼物架豆豆礼物',
        image: 'A5.png',
    },
    {
        resource_id: 'A6',
        name: '小拳拳',
        type: 'gift',
        mark: '礼物',
        module: '黑金收藏厅',
        unit: '个',
        price: 10,
        special_type: '礼物架豆豆礼物',
        image: 'A6.png',
    },
    {
        resource_id: 'A7',
        name: '么么哒',
        type: 'gift',
        mark: '礼物',
        module: '黑金收藏厅',
        unit: '个',
        price: 1000,
        special_type: '礼物架豆豆礼物',
        image: 'A7.png',
    },
    {
        resource_id: 'A8',
        name: '爱心冲击波',
        type: 'gift',
        mark: '礼物',
        module: '黑金收藏厅',
        unit: '个',
        price: 12000,
        special_type: '礼物架豆豆礼物',
        image: 'A8.png',
    },
    {
        resource_id: 'HD_love1',
        name: '帽子猫耳男',
        type: 'headwear',
        mark: '麦位框',
        module: '誓约卡',
        unit: '天',
        special_type: 'CP',
        image: 'HD_love1.png',
    },
    {
        resource_id: 'HD_love2',
        name: '帽子猫耳女',
        type: 'headwear',
        mark: '麦位框',
        module: '誓约卡',
        unit: '天',
        special_type: 'CP',
        image: 'HD_love2.png',
    },
    {
        resource_id: 'ENT_love',
        name: '8月七夕LV4',
        type: 'mount',
        mark: '坐骑',
        module: '誓约卡',
        unit: '天',
        special_type: '融合',
        image: 'ENT_love.png',
    },
    {
        resource_id: 'BDG_love1',
        name: '个_娱乐5月下1965',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '誓约卡',
        unit: '天',
        special_type: '',
        image: 'BDG_love1.png',
    },
    {
        resource_id: 'PWD_love',
        name: '弹_lv3机甲1520',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '誓约卡',
        unit: '次',
        special_type: '双用户展示',
        image: 'PWD_love.png',
    },
    {
        resource_id: 'AMCN_love',
        name: '真爱礼物',
        type: 'breaking_news',
        mark: '全服公告',
        module: '誓约卡',
        unit: '条',
        special_type: '房间内外跳转指定活动页面',
        image: 'AMCN_love.png',
    },
    {
        resource_id: 'CRD_love',
        name: '限定羁绊卡',
        type: 'other',
        mark: '成就卡',
        module: '誓约卡',
        unit: '天',
        special_type: '',
        image: 'CRD_love.png',
    },
    {
        resource_id: 'BDG_sc',
        name: '个_4月上贵族节1578',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '誓约卡',
        unit: '天',
        special_type: '',
        image: 'BDG_sc.png',
    },
    {
        resource_id: 'ENT_sc',
        name: '五月初夏亭子',
        type: 'mount',
        mark: '坐骑',
        module: '誓约卡',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_sc.png',
    },
    {
        resource_id: 'HD_sc',
        name: '春日舞曲1302',
        type: 'headwear',
        mark: '麦位框',
        module: '誓约卡',
        unit: '天',
        special_type: '自定义',
        image: 'HD_sc.png',
    },
    {
        resource_id: 'VR_sc',
        name: '福赠金榜送礼总榜前10',
        type: 'official_cert',
        mark: '大V认证',
        module: '誓约卡',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_sc.png',
    },
    {
        resource_id: 'gift_dingzhi',
        type: 'other',
        mark: '定制特权',
        module: '礼物定制权',
        unit: '天',
        special_type: '',
        image: 'gift_dingzhi.png',
    },
    {
        resource_id: 'PWD_BD1',
        name: '8周年庆至尊神壕-总榜冠军',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '榜单',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_BD1.png',
    },
    {
        resource_id: 'PWD_BD2',
        name: '8周年庆至尊神壕-总榜亚军',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '榜单',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_BD2.png',
    },
    {
        resource_id: 'PWD_BD3',
        name: '8周年庆至尊神壕-总榜季军',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '榜单',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_BD3.png',
    },
    {
        resource_id: 'PWD_BD4',
        name: '8周年庆至尊神壕-日榜冠军',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '榜单',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_BD4.png',
    },
    {
        resource_id: 'AMCN_BD1',
        name: 'AMCN_BD1总榜',
        type: 'breaking_news',
        mark: '全服公告',
        module: '榜单',
        unit: '条',
        special_type: '',
        image: 'AMCN_BD1.png',
    },
    {
        resource_id: 'AMCN_BD2',
        name: 'AMCN_BD2总榜',
        type: 'breaking_news',
        mark: '全服公告',
        module: '榜单',
        unit: '条',
        special_type: '',
        image: 'AMCN_BD2.png',
    },
    {
        resource_id: 'AMCN_BD3',
        name: 'AMCN_BD3总榜',
        type: 'breaking_news',
        mark: '全服公告',
        module: '榜单',
        unit: '条',
        special_type: '',
        image: 'AMCN_BD3.png',
    },
    {
        resource_id: 'AMCN_BD4',
        name: 'AMCN_BD4总榜',
        type: 'breaking_news',
        mark: '全服公告',
        module: '榜单',
        unit: '条',
        special_type: '',
        image: 'AMCN_BD4.png',
    },
    {
        resource_id: 'VR_B1',
        name: '南瓜魔王总榜TOP13',
        type: 'official_cert',
        mark: '大V认证',
        module: '榜单',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_B1.png',
    },
    {
        resource_id: 'VR_B2',
        name: '南瓜魔王总榜TOP14',
        type: 'official_cert',
        mark: '大V认证',
        module: '榜单',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_B2.png',
    },
    {
        resource_id: 'VR_B3',
        name: '南瓜魔王总榜TOP15',
        type: 'official_cert',
        mark: '大V认证',
        module: '榜单',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_B3.png',
    },
    {
        resource_id: 'VR_B4',
        name: '南瓜魔王总榜TOP16',
        type: 'official_cert',
        mark: '大V认证',
        module: '榜单',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_B4.png',
    },
    {
        resource_id: 'VR_B5',
        name: '南瓜魔王总榜TOP17',
        type: 'official_cert',
        mark: '大V认证',
        module: '榜单',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_B5.png',
    },
    {
        resource_id: 'HD_BD1',
        name: '春日舞曲1300',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD1.png',
    },
    {
        resource_id: 'HD_BD2',
        name: '麦_营收6月末2150A',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD2.png',
    },
    {
        resource_id: 'HD_BD3',
        name: '麦_6月中营收活动2141',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD3.png',
    },
    {
        resource_id: 'HD_BD4',
        name: '麦_lyxyshd2215',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD4.png',
    },
    {
        resource_id: 'HD_BD5',
        name: '麦_娱乐5月下1959',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD5.png',
    },
    {
        resource_id: 'HD_BD6',
        name: '麦_娱乐5月下1960',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD6.png',
    },
    {
        resource_id: 'HD_BD7',
        name: '麦_5月初夏活动1656',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD7.png',
    },
    {
        resource_id: 'HD_BD8',
        name: '麦_5月初夏活动1654',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD8.png',
    },
    {
        resource_id: 'HD_BD9',
        name: '25年四月下lv3',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD9.png',
    },
    {
        resource_id: 'ENT_BDX1',
        name: '劳动节lv5坐骑',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX1.png',
    },
    {
        resource_id: 'ENT_BDX2',
        name: '坐_4月中营收1773',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX2.png',
    },
    {
        resource_id: 'ENT_BDX3',
        name: '25年四月下lv4蓝',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX3.png',
    },
    {
        resource_id: 'ENT_BDX4',
        name: '25年四月下4.1紫',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX4.png',
    },
    {
        resource_id: 'ENT_BDX5',
        name: '贵族节1585',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX5.png',
    },
    {
        resource_id: 'ENT_BDX6',
        name: '上贵族节活动1584',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX6.png',
    },
    {
        resource_id: 'ENT_BDX7',
        name: '25年愚人节小猴',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX7.png',
    },
    {
        resource_id: 'ENT_BDX8',
        name: '春日舞曲lv3精灵',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX8.png',
    },
    {
        resource_id: 'ENT_BDX9',
        name: '春日舞曲钢琴男',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX9.png',
    },
    {
        resource_id: 'ENT_BDY1',
        name: '春日舞会lv4.1坐',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY1.png',
    },
    {
        resource_id: 'ENT_BDY2',
        name: '1299-lv4.1',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY2.png',
    },
    {
        resource_id: 'ENT_BDY3',
        name: '3月中-Lv5坐骑',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY3.png',
    },
    {
        resource_id: 'ENT_BDY4',
        name: '8月海豚探险海底',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY4.png',
    },
    {
        resource_id: 'ENT_BDY5',
        name: '男神评选坐骑H',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY5.png',
    },
    {
        resource_id: 'ENT_BDY6',
        name: '贵族坐骑c改',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY6.png',
    },
    {
        resource_id: 'ENT_BDY7',
        name: '贵族lv4.1',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY7.png',
    },
    {
        resource_id: 'ENT_BDY8',
        name: '五一活动坐骑2B文案',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY8.png',
    },
    {
        resource_id: 'ENT_BDY9',
        name: '五一坐骑1A文案',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY9.png',
    },
];

export const rewardMap = {
    card_1: {
        resource_id: 'card_1',
        name: '浮光新贵',
        type: 'other',
        mark: '成就卡',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'card_1.png',
    },
    card_2: {
        resource_id: 'card_2',
        name: '蔚蓝尊者',
        type: 'other',
        mark: '成就卡',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'card_2.png',
    },
    card_3: {
        resource_id: 'card_3',
        name: '紫曜尊使',
        type: 'other',
        mark: '成就卡',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'card_3.png',
    },
    card_4: {
        resource_id: 'card_4',
        name: '赤金名爵',
        type: 'other',
        mark: '成就卡',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'card_4.png',
    },
    card_5: {
        resource_id: 'card_5',
        name: '幻金圣尊',
        type: 'other',
        mark: '成就卡',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'card_5.png',
    },
    card_6: {
        resource_id: 'card_6',
        name: '传奇神尊',
        type: 'other',
        mark: '成就卡',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'card_6.png',
    },
    BDG_lv1: {
        resource_id: 'BDG_lv1',
        name: '完美人格',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'BDG_lv1.png',
    },
    BDG_lv2: {
        resource_id: 'BDG_lv2',
        name: '天下萌主',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'BDG_lv2.png',
    },
    BDG_lv3: {
        resource_id: 'BDG_lv3',
        name: '迎财纳福',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'BDG_lv3.png',
    },
    BDG_lv4: {
        resource_id: 'BDG_lv4',
        name: '浪漫超标',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'BDG_lv4.png',
    },
    BDG_lv5: {
        resource_id: 'BDG_lv5',
        name: '甜度过载',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'BDG_lv5.png',
    },
    BDG_lv6: {
        resource_id: 'BDG_lv6',
        name: '个_直播4月中春季赛1519',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'BDG_lv6.png',
    },
    BDG_X: {
        resource_id: 'BDG_X',
        name: '个_电竞推广1541',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '传奇加冕馆（定制）',
        unit: '天',
        special_type: '',
        image: 'BDG_X.png',
    },
    ENT_lv1: {
        resource_id: 'ENT_lv1',
        name: '男小猪',
        type: 'mount',
        mark: '坐骑',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_lv1.png',
    },
    ENT_lv2: {
        resource_id: 'ENT_lv2',
        name: '巴士',
        type: 'mount',
        mark: '坐骑',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_lv2.png',
    },
    ENT_lv3: {
        resource_id: 'ENT_lv3',
        name: '787露营温泉',
        type: 'mount',
        mark: '坐骑',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_lv3.png',
    },
    ENT_lv4: {
        resource_id: 'ENT_lv4',
        name: '男小猪',
        type: 'mount',
        mark: '坐骑',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_lv4.png',
    },
    ENT_lv5: {
        resource_id: 'ENT_lv5',
        name: '巴士',
        type: 'mount',
        mark: '坐骑',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_lv5.png',
    },
    ENT_lv6: {
        resource_id: 'ENT_lv6',
        name: '787露营温泉',
        type: 'mount',
        mark: '坐骑',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_lv6.png',
    },
    HD_lv1: {
        resource_id: 'HD_lv1',
        name: '0.1元lv2冰沙',
        type: 'headwear',
        mark: '麦位框',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'HD_lv1.png',
    },
    HD_lv2: {
        resource_id: 'HD_lv2',
        name: '0.1元lv2葡萄',
        type: 'headwear',
        mark: '麦位框',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'HD_lv2.png',
    },
    HD_lv3: {
        resource_id: 'HD_lv3',
        name: '0.1元lv2咖啡',
        type: 'headwear',
        mark: '麦位框',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '',
        image: 'HD_lv3.png',
    },
    HD_lv4: {
        resource_id: 'HD_lv4',
        name: '25年lv3愚人节',
        type: 'headwear',
        mark: '麦位框',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'HD_lv4.png',
    },
    HD_lv5: {
        resource_id: 'HD_lv5',
        name: '春日舞曲1302',
        type: 'headwear',
        mark: '麦位框',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'HD_lv5.png',
    },
    HD_lv6: {
        resource_id: 'HD_lv6',
        name: '春日舞曲1300',
        type: 'headwear',
        mark: '麦位框',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '自定义',
        image: 'HD_lv6.png',
    },
    VR_1: {
        resource_id: 'VR_1',
        name: '福赠金榜送礼总榜前10',
        type: 'official_cert',
        mark: '大V认证',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_1.png',
    },
    VR_2: {
        resource_id: 'VR_2',
        name: '福赠金榜送礼总榜前20',
        type: 'official_cert',
        mark: '大V认证',
        module: '传奇加冕馆',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_2.png',
    },
    PWD_lv1: {
        resource_id: 'PWD_lv1',
        name: '捕猎大丰收',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '传奇加冕馆',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_lv1.png',
    },
    PWD_lv2: {
        resource_id: 'PWD_lv2',
        name: '蔚蓝尊者现世',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '传奇加冕馆',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_lv2.png',
    },
    PWD_lv3: {
        resource_id: 'PWD_lv3',
        name: '弹_lv2海螺1499',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '传奇加冕馆',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_lv3.png',
    },
    PWD_lv4: {
        resource_id: 'PWD_lv4',
        name: '大R会场赤金名爵',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '传奇加冕馆',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_lv4.png',
    },
    PWD_lv5: {
        resource_id: 'PWD_lv5',
        name: '捕猎大丰收',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '传奇加冕馆',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_lv5.png',
    },
    PWD_lv6: {
        resource_id: 'PWD_lv6',
        name: '捕获复制器',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '传奇加冕馆',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_lv6.png',
    },
    AMCN_lv1: {
        resource_id: 'AMCN_lv1',
        name: '传奇加冕馆浮光新贵',
        type: 'breaking_news',
        mark: '全服公告',
        module: '传奇加冕馆',
        unit: '条',
        special_type: '',
        image: 'AMCN_lv1.png',
    },
    AMCN_lv2: {
        resource_id: 'AMCN_lv2',
        name: '传奇加冕馆蔚蓝尊者',
        type: 'breaking_news',
        mark: '全服公告',
        module: '传奇加冕馆',
        unit: '条',
        special_type: '',
        image: 'AMCN_lv2.png',
    },
    AMCN_lv3: {
        resource_id: 'AMCN_lv3',
        name: '传奇加冕馆紫耀尊使',
        type: 'breaking_news',
        mark: '全服公告',
        module: '传奇加冕馆',
        unit: '条',
        special_type: '',
        image: 'AMCN_lv3.png',
    },
    AMCN_lv4: {
        resource_id: 'AMCN_lv4',
        name: '传奇加冕馆赤金名爵',
        type: 'breaking_news',
        mark: '全服公告',
        module: '传奇加冕馆',
        unit: '条',
        special_type: '',
        image: 'AMCN_lv4.png',
    },
    AMCN_lv5: {
        resource_id: 'AMCN_lv5',
        name: '传奇加冕馆幻金圣尊',
        type: 'breaking_news',
        mark: '全服公告',
        module: '传奇加冕馆',
        unit: '条',
        special_type: '',
        image: 'AMCN_lv5.png',
    },
    AMCN_lv6: {
        resource_id: 'AMCN_lv6',
        name: '传奇加冕馆传奇神尊',
        type: 'breaking_news',
        mark: '全服公告',
        module: '传奇加冕馆',
        unit: '条',
        special_type: '',
        image: 'AMCN_lv6.png',
    },
    gift_1314: {
        resource_id: 'gift_1314',
        name: '营收520',
        type: 'present_privilege',
        mark: '礼物赠送权',
        module: '传奇加冕馆',
        unit: '天',
        price: 52000,
        special_type: '常规礼物赠送权',
        image: 'gift_1314.png',
    },
    gift_3000: {
        resource_id: 'gift_3000',
        name: '壕友会1314',
        type: 'present_privilege',
        mark: '礼物赠送权',
        module: '传奇加冕馆',
        unit: '天',
        price: 131400,
        special_type: '常规礼物赠送权',
        image: 'gift_3000.png',
    },
    gift_5000: {
        resource_id: 'gift_5000',
        name: '礼_lyxyshd2226',
        type: 'present_privilege',
        mark: '礼物赠送权',
        module: '传奇加冕馆',
        unit: '天',
        price: 10000,
        special_type: '常规礼物赠送权',
        image: 'gift_5000.png',
    },
    gift_1w: {
        resource_id: 'gift_1w',
        name: '娱乐5月下100',
        type: 'present_privilege',
        mark: '礼物赠送权',
        module: '传奇加冕馆',
        unit: '天',
        price: 10000,
        special_type: '常规礼物赠送权',
        image: 'gift_1w.png',
    },
    ENT_C1: {
        resource_id: 'ENT_C1',
        name: '坐_娱乐5月下1963',
        type: 'mount',
        mark: '坐骑',
        module: '惊喜秘宝阁',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_C1.png',
    },
    HD_C1: {
        resource_id: 'HD_C1',
        name: '25年lv3愚人节',
        type: 'headwear',
        mark: '麦位框',
        module: '惊喜秘宝阁',
        unit: '天',
        special_type: '自定义',
        image: 'HD_C1.png',
    },
    BDG_C1: {
        resource_id: 'BDG_C1',
        name: '个_4月上贵族节1579',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '惊喜秘宝阁',
        unit: '天',
        special_type: '',
        image: 'BDG_C1.png',
    },
    PKG_C1: {
        resource_id: 'PKG_C1',
        name: '甜蜜花海',
        type: 'package',
        mark: '包裹',
        module: '惊喜秘宝阁',
        unit: '个',
        price: 50000,
        special_type: '豆豆礼物',
        image: 'PKG_C1.png',
    },
    D1: {
        resource_id: 'D1',
        name: '惊喜秘宝阁D1',
        type: 'breaking_news',
        mark: '全服公告',
        module: '惊喜秘宝阁',
        unit: '条',
        special_type: '',
        image: 'D1.png',
    },
    A1: {
        resource_id: 'A1',
        name: '凤舞迎春',
        type: 'other',
        mark: '帝王套礼物',
        module: '黑金收藏厅',
        unit: '天',
        price: 999900,
        special_type: '礼物架豆豆礼物',
        image: 'A1.png',
    },
    A2: {
        resource_id: 'A2',
        name: '鎏金华彩99',
        type: 'other',
        mark: '帝王套礼物',
        module: '黑金收藏厅',
        unit: '天',
        price: 1388000,
        special_type: '礼物架豆豆礼物',
        image: 'A2.png',
    },
    A3: {
        resource_id: 'A3',
        name: '真爱帝王套',
        type: 'other',
        mark: '帝王套礼物',
        module: '黑金收藏厅',
        unit: '天',
        price: 1314520,
        special_type: '礼物架豆豆礼物',
        image: 'A3.png',
    },
    A4: {
        resource_id: 'A4',
        name: '新帝王套',
        type: 'other',
        mark: '帝王套礼物',
        module: '黑金收藏厅',
        unit: '天',
        price: 888800,
        special_type: '礼物架豆豆礼物',
        image: 'A4.png',
    },
    A5: {
        resource_id: 'A5',
        name: '水晶鞋',
        type: 'gift',
        mark: '礼物',
        module: '黑金收藏厅',
        unit: '个',
        price: 5000,
        special_type: '礼物架豆豆礼物',
        image: 'A5.png',
    },
    A6: {
        resource_id: 'A6',
        name: '小拳拳',
        type: 'gift',
        mark: '礼物',
        module: '黑金收藏厅',
        unit: '个',
        price: 10,
        special_type: '礼物架豆豆礼物',
        image: 'A6.png',
    },
    A7: {
        resource_id: 'A7',
        name: '么么哒',
        type: 'gift',
        mark: '礼物',
        module: '黑金收藏厅',
        unit: '个',
        price: 1000,
        special_type: '礼物架豆豆礼物',
        image: 'A7.png',
    },
    A8: {
        resource_id: 'A8',
        name: '爱心冲击波',
        type: 'gift',
        mark: '礼物',
        module: '黑金收藏厅',
        unit: '个',
        price: 12000,
        special_type: '礼物架豆豆礼物',
        image: 'A8.png',
    },
    HD_love1: {
        resource_id: 'HD_love1',
        name: '帽子猫耳男',
        type: 'headwear',
        mark: '麦位框',
        module: '誓约卡',
        unit: '天',
        special_type: 'CP',
        image: 'HD_love1.png',
    },
    HD_love2: {
        resource_id: 'HD_love2',
        name: '帽子猫耳女',
        type: 'headwear',
        mark: '麦位框',
        module: '誓约卡',
        unit: '天',
        special_type: 'CP',
        image: 'HD_love2.png',
    },
    ENT_love: {
        resource_id: 'ENT_love',
        name: '8月七夕LV4',
        type: 'mount',
        mark: '坐骑',
        module: '誓约卡',
        unit: '天',
        special_type: '融合',
        image: 'ENT_love.png',
    },
    BDG_love1: {
        resource_id: 'BDG_love1',
        name: '个_娱乐5月下1965',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '誓约卡',
        unit: '天',
        special_type: '',
        image: 'BDG_love1.png',
    },
    PWD_love: {
        resource_id: 'PWD_love',
        name: '弹_lv3机甲1520',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '誓约卡',
        unit: '次',
        special_type: '双用户展示',
        image: 'PWD_love.png',
    },
    AMCN_love: {
        resource_id: 'AMCN_love',
        name: '真爱礼物',
        type: 'breaking_news',
        mark: '全服公告',
        module: '誓约卡',
        unit: '条',
        special_type: '房间内外跳转指定活动页面',
        image: 'AMCN_love.png',
    },
    CRD_love: {
        resource_id: 'CRD_love',
        name: '限定羁绊卡',
        type: 'other',
        mark: '成就卡',
        module: '誓约卡',
        unit: '天',
        special_type: '',
        image: 'CRD_love.png',
    },
    BDG_sc: {
        resource_id: 'BDG_sc',
        name: '个_4月上贵族节1578',
        type: 'user_plate',
        mark: '个人铭牌',
        module: '誓约卡',
        unit: '天',
        special_type: '',
        image: 'BDG_sc.png',
    },
    ENT_sc: {
        resource_id: 'ENT_sc',
        name: '五月初夏亭子',
        type: 'mount',
        mark: '坐骑',
        module: '誓约卡',
        unit: '天',
        special_type: '自定义',
        image: 'ENT_sc.png',
    },
    HD_sc: {
        resource_id: 'HD_sc',
        name: '春日舞曲1302',
        type: 'headwear',
        mark: '麦位框',
        module: '誓约卡',
        unit: '天',
        special_type: '自定义',
        image: 'HD_sc.png',
    },
    VR_sc: {
        resource_id: 'VR_sc',
        name: '福赠金榜送礼总榜前10',
        type: 'official_cert',
        mark: '大V认证',
        module: '誓约卡',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_sc.png',
    },
    gift_dingzhi: {
        resource_id: 'gift_dingzhi',
        type: 'other',
        mark: '定制特权',
        module: '礼物定制权',
        unit: '天',
        special_type: '',
        image: 'gift_dingzhi.png',
    },
    PWD_BD1: {
        resource_id: 'PWD_BD1',
        name: '8周年庆至尊神壕-总榜冠军',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '榜单',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_BD1.png',
    },
    PWD_BD2: {
        resource_id: 'PWD_BD2',
        name: '8周年庆至尊神壕-总榜亚军',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '榜单',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_BD2.png',
    },
    PWD_BD3: {
        resource_id: 'PWD_BD3',
        name: '8周年庆至尊神壕-总榜季军',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '榜单',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_BD3.png',
    },
    PWD_BD4: {
        resource_id: 'PWD_BD4',
        name: '8周年庆至尊神壕-日榜冠军',
        type: 'pop_window',
        mark: '房间弹窗',
        module: '榜单',
        unit: '次',
        special_type: '单用户展示',
        image: 'PWD_BD4.png',
    },
    AMCN_BD1: {
        resource_id: 'AMCN_BD1',
        name: 'AMCN_BD1总榜',
        type: 'breaking_news',
        mark: '全服公告',
        module: '榜单',
        unit: '条',
        special_type: '',
        image: 'AMCN_BD1.png',
    },
    AMCN_BD2: {
        resource_id: 'AMCN_BD2',
        name: 'AMCN_BD2总榜',
        type: 'breaking_news',
        mark: '全服公告',
        module: '榜单',
        unit: '条',
        special_type: '',
        image: 'AMCN_BD2.png',
    },
    AMCN_BD3: {
        resource_id: 'AMCN_BD3',
        name: 'AMCN_BD3总榜',
        type: 'breaking_news',
        mark: '全服公告',
        module: '榜单',
        unit: '条',
        special_type: '',
        image: 'AMCN_BD3.png',
    },
    AMCN_BD4: {
        resource_id: 'AMCN_BD4',
        name: 'AMCN_BD4总榜',
        type: 'breaking_news',
        mark: '全服公告',
        module: '榜单',
        unit: '条',
        special_type: '',
        image: 'AMCN_BD4.png',
    },
    VR_B1: {
        resource_id: 'VR_B1',
        name: '南瓜魔王总榜TOP13',
        type: 'official_cert',
        mark: '大V认证',
        module: '榜单',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_B1.png',
    },
    VR_B2: {
        resource_id: 'VR_B2',
        name: '南瓜魔王总榜TOP14',
        type: 'official_cert',
        mark: '大V认证',
        module: '榜单',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_B2.png',
    },
    VR_B3: {
        resource_id: 'VR_B3',
        name: '南瓜魔王总榜TOP15',
        type: 'official_cert',
        mark: '大V认证',
        module: '榜单',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_B3.png',
    },
    VR_B4: {
        resource_id: 'VR_B4',
        name: '南瓜魔王总榜TOP16',
        type: 'official_cert',
        mark: '大V认证',
        module: '榜单',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_B4.png',
    },
    VR_B5: {
        resource_id: 'VR_B5',
        name: '南瓜魔王总榜TOP17',
        type: 'official_cert',
        mark: '大V认证',
        module: '榜单',
        unit: '天',
        special_type: '红v认证',
        image: 'VR_B5.png',
    },
    HD_BD1: {
        resource_id: 'HD_BD1',
        name: '春日舞曲1300',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD1.png',
    },
    HD_BD2: {
        resource_id: 'HD_BD2',
        name: '麦_营收6月末2150A',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD2.png',
    },
    HD_BD3: {
        resource_id: 'HD_BD3',
        name: '麦_6月中营收活动2141',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD3.png',
    },
    HD_BD4: {
        resource_id: 'HD_BD4',
        name: '麦_lyxyshd2215',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD4.png',
    },
    HD_BD5: {
        resource_id: 'HD_BD5',
        name: '麦_娱乐5月下1959',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD5.png',
    },
    HD_BD6: {
        resource_id: 'HD_BD6',
        name: '麦_娱乐5月下1960',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD6.png',
    },
    HD_BD7: {
        resource_id: 'HD_BD7',
        name: '麦_5月初夏活动1656',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD7.png',
    },
    HD_BD8: {
        resource_id: 'HD_BD8',
        name: '麦_5月初夏活动1654',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD8.png',
    },
    HD_BD9: {
        resource_id: 'HD_BD9',
        name: '25年四月下lv3',
        type: 'headwear',
        mark: '麦位框',
        module: '榜单',
        unit: '天',
        special_type: '自定义',
        image: 'HD_BD9.png',
    },
    ENT_BDX1: {
        resource_id: 'ENT_BDX1',
        name: '劳动节lv5坐骑',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX1.png',
    },
    ENT_BDX2: {
        resource_id: 'ENT_BDX2',
        name: '坐_4月中营收1773',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX2.png',
    },
    ENT_BDX3: {
        resource_id: 'ENT_BDX3',
        name: '25年四月下lv4蓝',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX3.png',
    },
    ENT_BDX4: {
        resource_id: 'ENT_BDX4',
        name: '25年四月下4.1紫',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX4.png',
    },
    ENT_BDX5: {
        resource_id: 'ENT_BDX5',
        name: '贵族节1585',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX5.png',
    },
    ENT_BDX6: {
        resource_id: 'ENT_BDX6',
        name: '上贵族节活动1584',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX6.png',
    },
    ENT_BDX7: {
        resource_id: 'ENT_BDX7',
        name: '25年愚人节小猴',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX7.png',
    },
    ENT_BDX8: {
        resource_id: 'ENT_BDX8',
        name: '春日舞曲lv3精灵',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX8.png',
    },
    ENT_BDX9: {
        resource_id: 'ENT_BDX9',
        name: '春日舞曲钢琴男',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '女',
        image: 'ENT_BDX9.png',
    },
    ENT_BDY1: {
        resource_id: 'ENT_BDY1',
        name: '春日舞会lv4.1坐',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY1.png',
    },
    ENT_BDY2: {
        resource_id: 'ENT_BDY2',
        name: '1299-lv4.1',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY2.png',
    },
    ENT_BDY3: {
        resource_id: 'ENT_BDY3',
        name: '3月中-Lv5坐骑',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY3.png',
    },
    ENT_BDY4: {
        resource_id: 'ENT_BDY4',
        name: '8月海豚探险海底',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY4.png',
    },
    ENT_BDY5: {
        resource_id: 'ENT_BDY5',
        name: '男神评选坐骑H',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY5.png',
    },
    ENT_BDY6: {
        resource_id: 'ENT_BDY6',
        name: '贵族坐骑c改',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY6.png',
    },
    ENT_BDY7: {
        resource_id: 'ENT_BDY7',
        name: '贵族lv4.1',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY7.png',
    },
    ENT_BDY8: {
        resource_id: 'ENT_BDY8',
        name: '五一活动坐骑2B文案',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY8.png',
    },
    ENT_BDY9: {
        resource_id: 'ENT_BDY9',
        name: '五一坐骑1A文案',
        type: 'mount',
        mark: '坐骑',
        module: '榜单',
        unit: '天',
        special_type: '男',
        image: 'ENT_BDY9.png',
    },
};
