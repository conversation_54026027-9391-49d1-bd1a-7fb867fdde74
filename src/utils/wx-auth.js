import { createAxios } from './request';
import { loadScriptSync } from './index';
import { urlConfig } from '@/config/url-config';
import { curUrl } from '@/config/url';

const WX_APP_ID = 'wx9cf924351316d70f';
// 后台服务请求域名
const osType = myWebview.isIOS() ? 'ios' : 'android';
const baseURL = urlConfig[`node_common_${osType}`].prod;

const request = createAxios({ baseURL });

const fetchApi = ({ api, data = {}, config = {} }) =>
    request.post(api, data, config);

/**
 * @name parseUrlQuery 获取URL上的参数
 * @returns {object} 参数对象
 */
const parseUrlQuery = () => {
    const url = location.search;
    const urlParams = {};
    if (url.includes('?')) {
        const str = url.substring(1);
        const strs = str.split('&');
        for (let i = 0; i < strs.length; i++) {
            urlParams[strs[i].split('=')[0]] = decodeURIComponent(
                strs[i].split('=')[1],
            );
        }
    }
    return urlParams;
};

/**
 * @name filterParams 过滤参数中指定的key
 * @param {object} params 参数对象
 * @param {Array} list 需过滤的key列表
 */
const filterParams = (params, list = []) => {
    if (Object.keys(params).length < 1)
        return {};
    const res = Object.assign({}, params);
    Object.keys(res).forEach((key) => {
        if (list.includes(key)) {
            delete res[key];
        }
    });
    return res;
};

/**
 * @name params2search 参数对象转UrlSearch
 * @param {object} params 参数对象
 */
const params2search = (params) => {
    if (!params || Object.keys(params).length < 1)
        return '';
    return `?${Object.keys(params)
        .reduce(
            (accumulator, currentValue) =>
                `${accumulator}&${currentValue}=${params[currentValue]}`,
            '',
        )
        .slice(1)}`;
};

/**
 * @name pushUrlParams 更新location状态
 * @param {*} obj
 */
const pushUrlParams = (obj = {}) => {
    history.replaceState(
        {},
        '',
        location.origin + location.pathname + params2search(obj),
    );
};

/**
 * @name wxAuth 微信授权
 * @param {string} appid 公众号appi
 */
export const wxAuth = (appid = WX_APP_ID) => {
    const redirect
        = location.origin
        + location.pathname
        + params2search(filterParams(parseUrlQuery(), ['code', 'state']));
    // 获取code
    window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${encodeURIComponent(
        redirect,
    )}&response_type=code&scope=snsapi_userinfo&state=7#wechat_redirect`;
};

/**
 * @name getUserInfoByCode 获取用户信息(code)
 * @param {string} code
 * @param {string} appid
 * @returns {Promise<object>} 用户信息
 */
export const getUserInfoByCode = async (code, appid = WX_APP_ID) => {
    try {
        const data = await fetchApi({
            api: 'wx-base/api/wechat/wxGrantNotice',
            data: { appid, code },
            config: {
                withCredentials: true,
            },
        });
        return data;
    }
    catch {
        return false;
    }
};

/**
 * @name getUserInfoByCookies 获取用户信息(Cookies)
 * 由服务器来检查所携带的Cookies是否有效，来返回用户信息
 * @returns {Promise<object>} 用户信息
 */
export const getUserInfoByCookies = async () => {
    try {
        const data = await fetchApi({
            api: 'wx-base/api/wechat/userinfo',
            config: {
                withCredentials: true,
            },
        });
        return data;
    }
    catch {
        return false;
    }
};

/**
 * @name wxSDKInit 初始化微信SDK
 * @param {Array} sdkApiList 微信sdk api开启列表 https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#63
 */
export const wxSDKInit = (
    sdkApiList = [],
    appid = WX_APP_ID,
    url = window.location.href.split('#')[0],
) => {
    return new Promise(async (resolve) => {
        if (!myWebview.isInWx()) {
            resolve();
            return;
        }
        try {
            await loadScriptSync(
                'https://ga-album-cdnqn.52tt.com/web/lib/jweixin-1.6.0.js',
            );
            const { code, data } = await fetchApi({
                api: 'wx-base/api/wechat/signature',
                data: { appid, url },
            });
            if (code === 0) {
                window.wx.config({
                    debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                    appId: appid, // 必填，公众号的唯一标识
                    timestamp: data.timestamp, // 必填，生成签名的时间戳
                    nonceStr: data.noncestr, // 必填，生成签名的随机串
                    signature: data.signature, // 必填，签名
                    jsApiList: sdkApiList, // 必填，需要使用的JS接口列表
                    openTagList: ['wx-open-launch-app'], // 使用微信开放标签
                });
                window.wx.ready(() => {
                    // console.log('wechat init succeed!');
                    resolve();
                });
                window.wx.error(() => {
                    // console.log('wechat init failed!', data);
                    resolve();
                });
            }
            else {
                resolve();
            }
        }
        catch {
            resolve();
        }
    });
};

/**
 * @name wxInit 微信初始化
 * @param {Array} sdkApiList 微信sdk api开启列表 https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#63
 * @returns { Promise<object> } 返回微信用户信息 {code, msg, data}
 */
export const wxInit = async (sdkApiList, appid = WX_APP_ID) => {
    let initRes = { code: 0, msg: 'Waiting auth.', data: {} };
    const urlParams = parseUrlQuery();
    // 当连接上带有微信的code
    if (urlParams.code) {
        const codeRes = await getUserInfoByCode(urlParams.code, appid);
        if (codeRes) {
            initRes = codeRes;
        }
        else {
            return { code: -1, msg: 'Get userinfo failed by code.', data: {} };
        }
    }
    else {
        // 检查cookies的有效性
        const cookiesRes = await getUserInfoByCookies();
        if (cookiesRes) {
            if (cookiesRes.data && cookiesRes.data.regrant) {
                wxAuth(); // 拉起授权弹窗
                return initRes;
            }
            initRes = cookiesRes;
        }
        else {
            return {
                code: -2,
                msg: 'Get userinfo failed by cookies.',
                data: {},
            };
        }
    }

    try {
        await wxSDKInit(sdkApiList, appid);
        if (urlParams.code) {
            pushUrlParams(filterParams(parseUrlQuery(), ['code', 'state'])); // 清除微信code和state参数
        }
        return initRes;
    }
    catch {
        return { code: -3, msg: 'Wechat SDK init failed.', data: initRes };
    }
};

/**
 * 初始化第三方分享信息
 * @param {object} obj 分享信息
 * @param {string} obj.title 分享信息
 * @param {string} obj.content 分享信息
 * @param {string} obj.imageUrl 分享信息
 * @param {string} obj.url 分享信息
 */
export const initThirdShareSdk = ({
    title = '',
    content = '',
    imageUrl = '',
    url = curUrl,
} = {}) => {
    return new Promise(async (resolve) => {
        try {
            if (myWebview.isInWx()) {
                try {
                    await wxSDKInit(['updateAppMessageShareData']);
                    window.wx.updateAppMessageShareData({
                        title, // 分享标题
                        desc: content, // 分享描述
                        imgUrl: imageUrl, // 分享图标
                        link: url, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                        success() {
                            // console.log('分享信息设置成功');
                        },
                    });
                    resolve();
                }
                catch {
                    resolve();
                }
            }
            if (myWebview.isInQq()) {
                loadScriptSync(
                    'https://ga-album-cdnqn.52tt.com/web/lib/qqapi.js',
                ).then(() => {
                    try {
                        window.mqq.data.setShareInfo(
                            {
                                share_url: url,
                                title,
                                desc: content, // 分享描述
                                image_url: imageUrl,
                            },
                            (data) => {
                                if (data !== true) {
                                    console.warn('setShareInfo', data);
                                }
                            },
                        );
                        resolve();
                    }
                    catch {
                        resolve();
                    }
                });
            }
            resolve();
        }
        catch {
            resolve();
        }
    });
};
