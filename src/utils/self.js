import { env } from '@/config/url';

export function getDirectAvatarUrl(username) {
    const map = {
        dev: username => `https://testing-avatar.ttyuyin.com/v2/${username}/ver-20221230133705/small?t=${Math.random()}${Date.now()}`,
        testing: username => `https://testing-avatar.ttyuyin.com/v2/${username}/ver-20221230133705/small?t=${Math.random()}${Date.now()}`,
        gray: username => `https://avatar.52tt.com/v2/${username}/ver-17f1c25943b0c8cb/small?t=${Math.random()}${Date.now()}`,
        prod: username => `https://avatar.52tt.com/v2/${username}/ver-17f1c25943b0c8cb/small?t=${Math.random()}${Date.now()}`,
    };
    return map[env](username);
}

export async function getImageBase64(url) {
    return new Promise((resolve, reject) => {
        const image = new Image();
        image.onload = () => {
            const canvas = document.createElement('canvas');
            canvas.width = image.width;
            canvas.height = image.height;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(image, 0, 0, image.width, image.height);
            const ext = image.src.substring(image.src.lastIndexOf('.') + 1).toLowerCase();
            const dataURL = canvas.toDataURL(`image/${ext}`);
            resolve(dataURL);
        };
        image.onerror = (err) => {
            reject(err);
        };
        image.crossOrigin = 'Anonymous';
        image.src = url;
    });
}

export async function getAvatarBase64(username) {
    return new Promise((resolve) => {
        const url = getDirectAvatarUrl(username);
        getImageBase64(url)
            .then((res) => {
                resolve(res);
            })
            .catch(() => {
                resolve('');
            });
    });
}

export async function sleep(time) {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve();
        }, time);
    });
}
