<!-- src/components/common/drag-btn.vue -->
<template>
    <div
        ref="dragElement"
        class="drag-btn-container"
        :style="dragStyle"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
    >
        <slot></slot>
    </div>
</template>

<script setup>
// 缓存位置的存储key
const POSITION_STORAGE_KEY = 'drag-btn-position';

// 响应式数据
const dragElement = ref(null);
const isDragging = ref(false);
const startPosition = ref({ x: 0, y: 0 });
const currentPosition = ref({ x: 0, y: 0 });
const elementSize = ref({ width: 56, height: 55 });
const containerBounds = ref({ width: 0, height: 0 });

// 使用活动存储缓存位置
const savedPosition = useActivityStorage(POSITION_STORAGE_KEY, { x: 0, y: 0 });

// 计算拖拽元素的样式
const dragStyle = computed(() => ({
    position: 'absolute',
    left: `${currentPosition.value.x}px`,
    top: `${currentPosition.value.y}px`,
    width: `${elementSize.value.width}px`,
    height: `${elementSize.value.height}px`,
    zIndex: 10,
    cursor: isDragging.value ? 'grabbing' : 'grab',
    transition: isDragging.value ? 'none' : 'all 0.3s ease-out',
}));

/**
 * 获取容器边界信息
 */
function updateContainerBounds() {
    const container = dragElement.value?.parentElement;
    if (container) {
        const rect = container.getBoundingClientRect();
        containerBounds.value = {
            width: rect.width || window.innerWidth,
            height: rect.height || window.innerHeight,
        };
    }
    else {
        containerBounds.value = {
            width: window.innerWidth,
            height: window.innerHeight,
        };
    }
}

/**
 * 限制位置在容器内
 */
function constrainPosition(x, y) {
    const minX = 0;
    const maxX = containerBounds.value.width - elementSize.value.width;
    const minY = 10; // 预留头部高度
    const maxY = containerBounds.value.height - elementSize.value.height;

    return {
        x: Math.max(minX, Math.min(maxX, x)),
        y: Math.max(minY, Math.min(maxY, y)),
    };
}

/**
 * 自动吸附到两边
 */
function snapToSide(x, y) {
    const centerX = containerBounds.value.width / 2;
    const snapX = x < centerX ? 0 : containerBounds.value.width - elementSize.value.width;

    return {
        x: snapX,
        y,
    };
}

/**
 * 触摸开始处理
 */
function handleTouchStart(event) {
    if (!dragElement.value)
        return;

    event.preventDefault();
    isDragging.value = true;

    const touch = event.touches[0];
    const rect = dragElement.value.getBoundingClientRect();

    // 记录触摸点相对于元素的偏移
    startPosition.value = {
        x: touch.clientX - rect.left,
        y: touch.clientY - rect.top,
    };

    updateContainerBounds();
}

/**
 * 触摸移动处理
 */
function handleTouchMove(event) {
    if (!isDragging.value || !dragElement.value)
        return;

    event.preventDefault();

    const touch = event.touches[0];
    const container = dragElement.value.parentElement;
    const containerRect = container?.getBoundingClientRect() || { left: 0, top: 0 };

    // 计算新位置（相对于容器）
    const newX = touch.clientX - containerRect.left - startPosition.value.x;
    const newY = touch.clientY - containerRect.top - startPosition.value.y;

    // 限制在容器内
    const constrainedPosition = constrainPosition(newX, newY);
    currentPosition.value = constrainedPosition;
}

/**
 * 触摸结束处理
 */
function handleTouchEnd(event) {
    if (!isDragging.value)
        return;

    event.preventDefault();
    isDragging.value = false;

    // 自动吸附到两边
    const snappedPosition = snapToSide(currentPosition.value.x, currentPosition.value.y);
    currentPosition.value = snappedPosition;

    // 保存位置到缓存
    savedPosition.value = { ...snappedPosition };
}

/**
 * 初始化位置
 */
function initializePosition() {
    updateContainerBounds();

    // 从缓存中恢复位置，如果缓存位置超出边界则重新约束
    const constrainedPosition = constrainPosition(
        savedPosition.value.x,
        savedPosition.value.y,
    );

    currentPosition.value = constrainedPosition;

    // 如果约束后的位置与缓存不同，更新缓存
    if (constrainedPosition.x !== savedPosition.value.x
        || constrainedPosition.y !== savedPosition.value.y) {
        savedPosition.value = { ...constrainedPosition };
    }
}

// 监听窗口大小变化
const handleResize = useDebounceFn(() => {
    updateContainerBounds();
    const constrainedPosition = constrainPosition(
        currentPosition.value.x,
        currentPosition.value.y,
    );
    currentPosition.value = constrainedPosition;
    savedPosition.value = { ...constrainedPosition };
}, 300);

// 生命周期钩子
onMounted(() => {
    initializePosition();
    window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
});

// 监听缓存位置变化，同步到当前位置
watch(savedPosition, (newPosition) => {
    if (!isDragging.value) {
        const constrainedPosition = constrainPosition(newPosition.x, newPosition.y);
        currentPosition.value = constrainedPosition;
    }
}, { deep: true });
</script>

<style lang="less" scoped>
.drag-btn-container {
    position: relative;
    width: 100%;
    height: 100%;
    &:active {
        opacity: 0.8;
    }
}
</style>
