<template>
    <div class="box">
        <img
            ref="moving"
            class="absolute left-[0px] top-[334px] z-999 h-[55px] w-[56px]"
            src="@/assets/img/tab1/<EMAIL>"
            @touchmove="drag('moving', $event)"
            @touchstart="touchStartHandle('moving', $event)"
            @click="useEventBus('hint-modal').emit({ show: true })" />
    </div>
</template>

<script>
// const dragX = ref(0);
// const dragY = ref(0);
// const trueLoveTestPosition = localStorage.getItem('trueLoveTestPosition');

// const stopDrag = () => {
//     // 保存拖动位置到localStorage
//     localStorage.setItem('trueLoveTestPosition', JSON.stringify({
//         x: dragX.value,
//         y: dragY.value,
//     }));
// };

// if (trueLoveTestPosition) {
//     try {
//         const { x, y } = JSON.parse(trueLoveTestPosition);
//         dragX.value = x;
//         dragY.value = y;
//     }
//     catch (error) {

//     }
// }
export default {
    data() {
        return {
            coordinate: {
                client: {},
                elePosition: {},
            },
        };
    },
    methods: {

        touchStartHandle(refName, e) {
            let element = e.targetTouches[0];
            // 记录点击的坐标
            this.coordinate.client = {
                x: element.clientX,
                y: element.clientY,
            };
            // 记录需要移动的元素坐标
            this.coordinate.elePosition.left = this.$refs[refName].offsetLeft;
            this.coordinate.elePosition.top = this.$refs[refName].offsetTop;
        },
        drag(refName, e) {
            let element = e.targetTouches[0];
            // 根据初始client位置计算移动距离（元素移动位置+光标移动后的位置-光标点击时的初始位置）
            let x = this.coordinate.elePosition.left + (element.clientX - this.coordinate.client.x);
            let y = this.coordinate.elePosition.top + (element.clientY - this.coordinate.client.y);
            // 限制可移动距离，不超出可视区域
            x = x <= 0 ? 0 : x >= innerWidth - this.$refs[refName].offsetWidth ? innerWidth - this.$refs[refName].offsetWidth : x;
            // 50是头部的高度
            y = y <= 50 ? 50 : x >= innerHeight - this.$refs[refName].offsetHeight ? innerHeight - this.$refs[refName].offsetHeight : y;
            // 移动当前元素
            this.$refs[refName].style.left = `${x}px`;
            this.$refs[refName].style.top = `${y}px`;
        },
        /**
         * 返回顶部
         */
        goTop() {
            document.getElementsByClassName('box')[0].scrollTop = 0;
        },

    },
};
</script>

<style scoped>

</style>
