<template>
    <div
        class="x-scroll-wrapper box-border flex items-center"
        :style="{ width, height }"
        @mousedown.passive="startDrag"
        @mousemove.passive="onDrag"
        @mouseup.passive="endDrag"
        @mouseleave.passive="endDrag"
        @touchstart.passive="startDrag"
        @touchmove.prevent="onDrag"
        @touchend.passive="endDrag"
        @touchcancel.passive="endDrag"
    >
        <div
            ref="scrollContent"
            class="scroll-content"
            :class="{ 'justify-center': !needScroll }"
            :style="{ transform: needScroll ? `translateX(${translateX}px)` : 'none' }"
        >
            <div
                ref="contentWrapper"
                class="content-wrapper">
                <slot></slot>
            </div>
            <div
                v-if="needScroll"
                class="content-wrapper">
                <slot></slot>
            </div>
        </div>
    </div>
</template>

<script setup>
import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue';

const props = defineProps({
    width: {
        type: String,
        default: '100%',
    },
    height: {
        type: String,
        default: '100%',
    },
    speed: {
        type: Number,
        default: 0.5,
    },
    delay: {
        type: Number,
        default: 1000, // 默认延迟为0，立即开始
    },
});

const scrollContent = ref(null);
const translateX = ref(0);
const isDragging = ref(false);
const startX = ref(0);
const lastX = ref(0);
const scrollTimer = ref(null);
const resumeTimer = ref(null);
const delayTimer = ref(null);
const contentWidth = ref(0);
const contentWrapper = ref(null);
const lastTime = ref(0);
const FPS = 120; // 控制帧率，数值越小滚动越慢
const interval = 1000 / FPS;
const needScroll = ref(false);
const observer = ref(null);

const stopScroll = () => {
    if (scrollTimer.value) {
        cancelAnimationFrame(scrollTimer.value);
        scrollTimer.value = null;
    }
    if (resumeTimer.value) {
        clearTimeout(resumeTimer.value);
        resumeTimer.value = null;
    }
    if (delayTimer.value) {
        clearTimeout(delayTimer.value);
        delayTimer.value = null;
    }
};

const scroll = (timestamp) => {
    if (isDragging.value || !contentWidth.value) {
        return;
    }

    if (timestamp - lastTime.value < interval) {
        scrollTimer.value = requestAnimationFrame(scroll);
        return;
    }

    translateX.value = Math.round((translateX.value - props.speed) * 100) / 100;

    if (Math.abs(translateX.value) >= contentWidth.value) {
        translateX.value = translateX.value + contentWidth.value;
    }

    lastTime.value = timestamp;
    scrollTimer.value = requestAnimationFrame(scroll);
};

const startScroll = () => {
    stopScroll();
    if (contentWidth.value > 0) {
        if (props.delay > 0) {
            delayTimer.value = setTimeout(() => {
                scrollTimer.value = requestAnimationFrame(scroll);
            }, props.delay);
        }
        else {
            scrollTimer.value = requestAnimationFrame(scroll);
        }
    }
};

const initScroll = () => {
    const content = contentWrapper.value;
    const container = scrollContent.value;
    if (content && container) {
        contentWidth.value = Math.round(content.offsetWidth);
        // 判断是否需要滚动
        needScroll.value = contentWidth.value > container.offsetWidth;
        if (needScroll.value) {
            startScroll();
        }
        else {
            stopScroll();
            translateX.value = 0;
        }
    }
};

const startDrag = (e) => {
    if (!contentWidth.value)
        return;

    isDragging.value = true;
    startX.value = e.type.includes('mouse')
        ? e.clientX
        : (e.touches?.[0]?.clientX ?? 0);
    lastX.value = translateX.value;
    stopScroll();
};

const onDrag = (e) => {
    if (!isDragging.value || !contentWidth.value)
        return;

    // 移动端阻止默认行为，避免页面滚动
    if (e.type === 'touchmove') {
        e.preventDefault();
    }

    const currentX = e.type.includes('mouse')
        ? e.clientX
        : (e.touches?.[0]?.clientX ?? startX.value);
    const diff = currentX - startX.value;

    let newTranslateX = Math.round((lastX.value + diff) * 100) / 100;

    // 当translateX接近0时，锁定在0，防止继续向左拖动
    if (newTranslateX > 0) {
        newTranslateX = 0;
    }
    // 当向左拖动超过内容宽度时，循环到起始位置
    else if (Math.abs(newTranslateX) > contentWidth.value) {
        newTranslateX = newTranslateX + contentWidth.value;
    }

    translateX.value = newTranslateX;
};

const endDrag = () => {
    if (!isDragging.value)
        return;

    isDragging.value = false;

    if (resumeTimer.value) {
        clearTimeout(resumeTimer.value);
    }

    resumeTimer.value = setTimeout(() => {
        if (!isDragging.value) {
            startScroll();
        }
    }, 1000);
};

let resizeTimer = null;
const handleResize = () => {
    if (resizeTimer) {
        clearTimeout(resizeTimer);
    }
    resizeTimer = setTimeout(() => {
        initScroll();
    }, 200);
};

const setupObserver = () => {
    if (contentWrapper.value) {
        observer.value = new MutationObserver(() => {
            setTimeout(() => {
                initScroll();
            }, 100);
        });

        observer.value.observe(contentWrapper.value, {
            childList: true,
            subtree: true,
            characterData: true,
        });
    }
};

onMounted(() => {
    nextTick(() => {
        initScroll();
        setupObserver();
        window.addEventListener('resize', handleResize);
    });
});

onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
    stopScroll();
    if (resizeTimer) {
        clearTimeout(resizeTimer);
    }
    if (observer.value) {
        observer.value.disconnect();
        observer.value = null;
    }
});
</script>

<style scoped>
.x-scroll-wrapper {
    position: relative;
    overflow: hidden;
    /* 添加移动端触摸优化 */
    touch-action: pan-y pinch-zoom;
    -webkit-overflow-scrolling: touch;
}

.scroll-content {
    display: flex;
    will-change: transform;
    backface-visibility: hidden;
    width: 100%;
    /* 添加移动端动画优化 */
    -webkit-transform: translateZ(0);
    -webkit-backface-visibility: hidden;
}

.scroll-content.justify-center {
    justify-content: center;
}

.content-wrapper {
    flex: 0 0 auto;
    display: flex;
}
</style>
