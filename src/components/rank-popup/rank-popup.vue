<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div
            v-if="isShow"
            class="rank-list">
            <div class="dates">
                <div class="dates-bg"></div>
                <RankDates
                    :key="isShow"
                    :initial-dates="['20250715', '20250716', '20250717', '20250718', '20250719', '20250720', '20250721', '20250722', '20250723', '20250724', '20250725', '20250726', '20250727', '20250728']"
                    :current-date="currentDate"
                    :show-total-when-mounted="showTotalWhenMounted"
                    @init-current-date="handleInitCurrentDate"
                    @update-current-date="handleSetCurrentDate"
                />
            </div>
            <van-list
                v-model:loading="loading"
                class="overflow-auto"
                :finished="!hasMore"
                offset="100"
                @load="handleLoadMore"
            >
                <div class="flex flex-shrink-0 flex-col items-center overflow-y-auto">
                    <div
                        class="bg-default relative z-1 flex flex-col items-center overflow-hidden px-[4px] pt-[14px]"
                        :class="isOpen ? 'w-[342.5px] h-[211.5px]' : 'w-[327.5px] h-[104.5px]'"
                        :style="`background-image: url(${isOpen ? requireImg('tab1/<EMAIL>') : requireImg('tab1/<EMAIL>')})`"
                    >
                        <img
                            v-if="isTotal"
                            class="h-[13.5px] w-[219.5px]"
                            src="@/assets/img/tab1/<EMAIL>"
                            alt="">
                        <img
                            v-else
                            class="h-[13.5px] w-[176px]"
                            src="@/assets/img/tab1/<EMAIL>"
                            alt="">
                        <div class="mt-[8px] text-center text-[11px] text-[#FFFFFF]">送出任意豆豆礼物（含背包）、在房停留累计流光值</div>
                        <div class="mt-[6px] text-center text-[11px] text-[#FFFFFF]">送礼1豆=1值；在房1小时=1值</div>
                        <broadcast
                            v-if="isOpen"
                            class="mx-[2px] mt-[12px] w-[300px] flex-shrink-0"
                            direction="x"
                            :allow-touch="true"
                            :data="shownRewards || []">
                            <template #default="{ item }">
                                <div class="bg-default mx-[4px] flex flex-shrink-0 flex-col items-center">
                                    <div
                                        class="flex-center bg-default flex-center relative h-[67.5px] w-[67px] flex text-[10px] text-[#FFFFFF]"
                                        :style="`background-image: url(${requireImg('tab3/<EMAIL>')})`">
                                        <div
                                            v-if="!item.double"
                                            class="bg-default absolute left-1/2 top-[-7px] h-[21px] w-[57px] flex flex justify-center pt-7 -translate-x-1/2"
                                            :style="`background-image: url(${requireImg('tab1/<EMAIL>')})`"
                                        >
                                            {{ item.tag }}
                                        </div>
                                        <div
                                            v-else
                                            class="bg-default absolute left-1/2 top-[-5px] h-[33.5px] w-[63.5px] flex flex justify-center pt-7 -translate-x-1/2"
                                            :style="`background-image: url(${requireImg('tab1/<EMAIL>')})`"
                                        >
                                            <div class="w-[60px] text-center">{{ item.tag }}</div>
                                        </div>
                                        <img
                                            :src="requireImg(getRewardInfo(item).imageUrl)"
                                            class="h-[50px] w-[50px]"
                                            alt="">
                                    </div>
                                    <div class="mt-[6px] text-[11px] text-[#FFF9DB] leading-[12px]">{{ getRewardInfo(item).name }}</div>
                                    <div class="text-[11px] text-[#FFF9DB] leading-[12px]">{{ item.mark || '' }}</div>
                                </div>
                            </template>
                        </broadcast>
                        <div
                            class="btn absolute bottom-[10px] right-1/2 h-[23px] flex translate-x-1/2 items-center justify-center whitespace-nowrap border-[0.75px] border-[#fffae0] border-[solid] rounded-[11.5px] px-[10px] text-[12px] text-[#4C0000] font-bold"
                            @click="isOpen = !isOpen"
                        >
                            {{ isOpen ? '收起' : '展开' }}TOP{{ isTotal ? 20 : 10 }}更多榜单奖励>
                        </div>
                    </div>
                    <div
                        class="content"
                        :class="{ 'no-self': !hasSelf }"
                    >
                        <div class="tops">
                            <div
                                v-for="(user, index) in listInfo.list.slice(0, 3)"
                                :key="`${index}-${user.userInfo?.uid}`"
                                :class="`top top-${index + 1}`">
                                <div class="bg"></div>
                                <div
                                    class="avatar-wrapper"
                                    @click.stop="() => toPerson(user.userInfo?.username)"></div>
                                <div
                                    class="avatar"
                                    @click.stop="() => toPerson(user.userInfo?.username)"
                                >
                                    <img :src="getAvatar(user.userInfo?.username || '')" />
                                </div>
                                <room-status
                                    v-if="user.channelInfo?.channelId"
                                    class="room-status"
                                    :channel-id="user.channelInfo?.channelId"
                                    :status="user.channelInfo?.status" />
                                <div class="nickname">
                                    {{ user.userInfo?.nickname }}
                                </div>
                                <div class="value flex">
                                    <span class="ext text-[11px] text-[#FFEC6F]">{{ ext }}</span>
                                    <span class="num">{{ omitValue(user.value || 0) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="user-list">
                            <div
                                v-for="(user, index) in listInfo.list"
                                :key="`${index}-${user.userInfo?.uid}`"
                                class="user"
                            >
                                <div class="left">
                                    <div class="user-rank">
                                        <div
                                            class="number-bg flex-center flex text-[15px]">
                                            {{ user.rank }}
                                        </div>
                                    </div>
                                    <div class="avatar">
                                        <div
                                            class="avatar-wrapper"
                                            @click.stop="() => toPerson(user.userInfo?.username)"></div>
                                        <div class="avatar-image">
                                            <img :src="getAvatar(user.userInfo?.username || '')" />
                                        </div>
                                        <room-status
                                            v-if="user.channelInfo?.channelId"
                                            :channel-id="user.channelInfo?.channelId"
                                            :status="user.channelInfo?.status"
                                            class="room-status" />
                                    </div>
                                    <div class="nickname">
                                        {{ user.userInfo?.nickname || '' }}
                                    </div>
                                </div>
                                <div class="right">
                                    <div class="value text-right">
                                        {{ omitValue(user.value || 0) }} <div class="text">{{ ext }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            v-if="hasSelf"
                            class="self">
                            <div class="me">
                                <div class="left">
                                    <div class="user-rank">
                                        <div class="flex-center shadow-text-gray mr-[2px] flex text-[15px]">
                                            {{ selfRank }}
                                        </div>
                                    </div>
                                    <div
                                        class="avatar"
                                        @click.stop="() => toPerson(listInfo.self?.userInfo?.username)">
                                        <div class="avatar-image">
                                            <img :src="getAvatar(listInfo.self?.userInfo?.username || '')" />
                                        </div>
                                    </div>
                                </div>
                                <div class="right">
                                    <div class="value">
                                        <div class="num">{{ omitValue(listInfo.self?.value || 0) }} <span class="">{{ ext }}</span></div>
                                    </div>
                                </div>
                                <div class="absolute right-[30px] top-[12px] text-[11px] text-[#0F0300]">{{ listInfo.self?.gtNextValueHuman || listInfo?.self.ltPrevValueHuman || '' }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </van-list>
        </div>
    </popup-container>
</template>

<script setup>
// 送礼总榜 ?rankPopup=1&rankPopupQueryType=1&rankPopupDateType=1
// 送礼日榜 ?rankPopup=1&rankPopupQueryType=1&rankPopupDateType=2&rankPopupDate=%s
// 收礼总榜 ?rankPopup=1&rankPopupQueryType=2&rankPopupDateType=1
// 收礼日榜 ?rankPopup=1&rankPopupQueryType=2&rankPopupDateType=2&rankPopupDate=%s
import RankDates from './modal-rank-list-dates.vue';
import { getSupremeRank as getRank } from '@/api';
import useLoading from '@/hooks/useLoading';

const isShow = ref(false);

useEventBus('rank-popup').on(({ show = true }) => {
    isShow.value = show;
});

const ONCE_KEY = `revenue-thanksgiving-day-2024-modal-rank-list-once-${myWebview.params.uid}`;

const DATE_TYPE = { TOTAL: 1, COMMON: 2 };

const loading = ref(false);

useEventBus('cp-rank').on(() => {
    query(true);
});

const currentDate = ref('');
const showTotalWhenMounted = ref(parseUrlQuery().rankPopupDateType === DATE_TYPE.TOTAL);
const expand = ref(false);
const listInfo = ref({ list: [], self: {}, page: 1, size: 20, total: 0 });
const queryParams = computed(() => ({ ...currentDate.value ? { date: currentDate.value } : {} }));
const isTotal = computed(() => !currentDate.value);
const ext = computed(() => '流光值');
const hasSelf = computed(() => !!listInfo.value.self?.userInfo?.uid);
const selfRank = computed(() => {
    if (!listInfo.value.self?.value || !listInfo.value.self?.rank) {
        return '100+';
    }
    return listInfo.value.self?.rank > 100 ? '100+' : listInfo.value.self?.rank || '-';
});

const isOpen = ref(false);

const shownRewards = computed(() => {
    let result = [];

    if (!currentDate.value) {
        result = [

            { ...getRewardInfo('PWD_BD1'), mark: '5位靓号', tag: 'TOP1且分值达到XX可得', double: true },
            { ...getRewardInfo('PWD_BD1'), tag: 'TOP3得' },
            { ...getRewardInfo('AMCN_BD1'), tag: 'TOP3得' },
            { ...getRewardInfo('HD_BD1'), tag: 'TOP1得' },
            { ...getRewardInfo('ENT_BDX1'), tag: 'TOP1得' },
            { ...getRewardInfo('ENT_BDY1'), tag: 'TOP1得' },
            { ...getRewardInfo('HD_BD2'), tag: 'TOP2得' },
            { ...getRewardInfo('ENT_BDX2'), tag: 'TOP2得' },
            { ...getRewardInfo('ENT_BDY2'), tag: 'TOP2得' },
            { ...getRewardInfo('HD_BD3'), tag: 'TOP3得' },
            { ...getRewardInfo('ENT_BDX3'), tag: 'TOP3得' },
            { ...getRewardInfo('ENT_BDY3'), tag: 'TOP3得' },
            { ...getRewardInfo('HD_BD4'), tag: 'TOP4-10得' },
            { ...getRewardInfo('ENT_BDX4'), tag: 'TOP4-10得' },
            { ...getRewardInfo('ENT_BDY4'), tag: 'TOP4-10得' },
            { ...getRewardInfo('HD_BD5'), tag: 'TOP11-20得' },
            { ...getRewardInfo('ENT_BDX5'), tag: 'TOP11-20得' },
            { ...getRewardInfo('ENT_BDY5'), tag: 'TOP11-20得' },
            { ...getRewardInfo('VR_B1'), tag: 'TOP20得' },
        ];
    }
    else {
        result = [
            { ...getRewardInfo('PWD_BD4'), tag: 'TOP1得' },
            { ...getRewardInfo('AMCN_BD4'), tag: 'TOP1得' },
            { ...getRewardInfo('HD_BD6'), tag: 'TOP1得' },
            { ...getRewardInfo('ENT_BDX6'), tag: 'TOP1得' },
            { ...getRewardInfo('ENT_BDY6'), tag: 'TOP1得' },
            { ...getRewardInfo('HD_BD7'), tag: 'TOP2得' },
            { ...getRewardInfo('ENT_BDX7'), tag: 'TOP2得' },
            { ...getRewardInfo('ENT_BDY7'), tag: 'TOP2得' },
            { ...getRewardInfo('HD_BD8'), tag: 'TOP3得' },
            { ...getRewardInfo('ENT_BDX8'), tag: 'TOP3得' },
            { ...getRewardInfo('ENT_BDY8'), tag: 'TOP3得' },
            { ...getRewardInfo('HD_BD9'), tag: 'TOP4-10得' },
            { ...getRewardInfo('ENT_BDX9'), tag: 'TOP4-10得' },
            { ...getRewardInfo('ENT_BDY9'), tag: 'TOP4-10得' },
        ];
    }
    return result;
});

function clearListInfo() {
    listInfo.value = { list: [], self: {}, page: 1, size: 20, total: 0 };
}

const hasMore = computed(() => {
    if (!listInfo.value.total)
        return false;
    return listInfo.value.list.length < listInfo.value.total;
});

const isLoading = ref(false);
async function query(loadMore = false) {
    if (loadMore && !listInfo.value.total)
        return;
    if (loadMore && ((!!listInfo.value.total && listInfo.value.list.length >= listInfo.value.total)))
        return;
    if (!loadMore) {
        clearListInfo();
    }
    isLoading.value = true;
    const [res] = await getRank({
        page: listInfo.value.page,
        size: listInfo.value.size,
        ...queryParams.value,
    });
    if (res?.code === 0) {
        const latestSizeList = res.data?.list || [];
        listInfo.value.list = listInfo.value.list
            .slice(0, (listInfo.value.page - 1) * listInfo.value.size)
            .concat(latestSizeList);
        listInfo.value.total = res.data?.total || 0;
        listInfo.value.self = res.data?.self || {};
        if (latestSizeList.length === listInfo.value.size) {
            listInfo.value.page += 1;
        }
    }
    isLoading.value = false;
}
// 加载更多
const handleLoadMore = async () => {
    if (isLoading.value || !hasMore.value) {
        loading.value = false;
        return;
    }
    try {
        await query(true);
    }
    finally {
        loading.value = false;
    }
};

onMounted(() => {
    // const once = window.localStorage.getItem(ONCE_KEY);
    // if (!once) {
    //     window.localStorage.setItem(ONCE_KEY, '1');
    //     expand.value = true;
    // }
});

onUnmounted(() => {
    clearListInfo();
    expand.value = false;
});

function handleInitCurrentDate({ dateText }) {
    currentDate.value = dateText || '';
    query();
}

function handleSetCurrentDate({ dateText }) {
    if (currentDate.value !== dateText) {
        currentDate.value = dateText;
        query();
    }
}
</script>

<style lang="less" scoped>
.left-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}
.rank-list {
    padding-top: 64px;
    overflow: hidden;
    position: relative;
    padding-bottom: 30px;
    background: url('@/assets/img/tab1/<EMAIL>') no-repeat center top;
    background-size: 100%;
    width: 375px;
    height: 675px;
    display: flex;
    align-items: center;
    flex-direction: column;
    position: relative;
    margin: 0 auto;

    .dates {
        position: relative;
        margin: 0px auto 0;
        width: 362.5px;

        .dates-bg {
            position: absolute;
            left: 0;
            top: 3px;
            background-image: url('@/assets/img/tab1/<EMAIL>');
            width: 362.5px;
            height: 22.5px;
            background-size: 100% 100%;
        }
    }
    .content {
        // .tb();
        position: relative;
        width: 100%;
        padding-bottom: 80px;
        &.no-self {
            padding-bottom: 10px;
        }
        .tops {
            width: 375px;
            height: 400px;
            // background-image: url('@/assets/img/<EMAIL>');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: center top;

            margin-top: 5px;
            position: relative;
            overflow: hidden;
            .top {
                position: absolute;
                width: 131.5px;
                height: 171px;
                .bg {
                    position: absolute;
                    left: 0;
                    top: 0;
                    z-index: 50;
                }
                .avatar {
                    .left-center();
                    background: #971b08;
                    top: 40px;
                    width: 53.5px;
                    height: 53.5px;
                    z-index: 50;
                    border-radius: 50%;
                    img {
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                    }
                }
                .nickname {
                    .one-line();
                    .left-center();
                    text-align: center;
                    top: 114px;
                    width: 80px;
                    z-index: 60;
                    font-size: 12px;
                    font-weight: bold;
                    color: #fff9d0;
                    letter-spacing: 0.3px;
                }
                .value {
                    .flex-center();
                    position: absolute;
                    z-index: 80;
                    top: 133px;
                    width: 100%;
                    font-size: 11px;
                    color: #ffec6f;
                    letter-spacing: 0.3px;
                }
                .room-status {
                    .left-center();
                    top: 60px;
                    z-index: 100;
                    width: 43.5px;
                    height: 14.5px;
                    font-size: 9px;
                }
                &.top-1 {
                    .left-center();
                    top: 0;
                    background-image: url('@/assets/img/tab1/<EMAIL>');
                    background-size: 100%;
                    width: 360px;
                    height: 247.5px;
                    .avatar {
                        top: 47px;
                        width: 56.5px;
                        height: 56.5px;
                    }
                    .bg {
                    }
                    .value {
                        top: 144px;
                    }
                    .room-status {
                        top: 70px;
                    }
                    .nickname {
                        top: 124px;
                    }
                }
                &.top-2 {
                    right: 170px;
                    top: 167px;
                    background-image: url('@/assets/img/tab1/<EMAIL>');
                    background-size: 100%;
                    width: 225.5px;
                    height: 220.5px;
                    .bg {
                    }
                    .avatar {
                    }
                    .nickname {
                    }
                }
                &.top-3 {
                    left: 170px;
                    top: 167px;
                    background-image: url('@/assets/img/tab1/<EMAIL>');
                    background-size: 100%;
                    width: 225.5px;
                    height: 220.5px;
                    .bg {
                    }
                    .avatar {
                    }
                    .nickname {
                    }
                }
            }
        }
        .user-list {
            width: 375px;
            position: relative;
            .user {
                background-image: url('@/assets/img/tab1/<EMAIL>');
                width: 357px;
                height: 68.5px;
                background-size: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 22px 0 0px;
                margin: auto;
                position: relative;
                margin-bottom: 4px;
                .left {
                    display: flex;
                    align-items: center;
                    .user-rank {
                        margin-left: 10px;
                        width: 20px;
                        height: 20px;
                        .flex-center();
                        height: 100%;
                        font-size: 11px;
                        font-weight: normal;
                        color: #ffffff;
                    }
                    .avatar {
                        position: relative;
                        margin-left: 10px;
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        border: 1px solid #feef9a;
                        .avatar-wrapper {
                            // border: 1px solid #ffffff;
                            // border-radius: 50%;
                            // width: 47px;
                            // height: 47px;
                            // position: absolute;
                            // left: 50%;
                            // top: 50%;
                            // transform: translate(-50%, -50%);
                            // z-index: 100;
                        }
                        .avatar-image {
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            z-index: 50;
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            img {
                                width: 100%;
                                height: 100%;
                                border-radius: 50%;
                            }
                        }
                        .room-status {
                            .left-center();
                            .flex-center();
                            width: 43.5px;
                            height: 14.5px;
                            bottom: -3.5px;
                            z-index: 150;
                            font-size: 8px;
                        }
                    }
                    .nickname {
                        .one-line();
                        margin-left: 8px;
                        width: 100px;
                        font-size: 12px;
                        font-weight: bold;
                        color: #ffffff;
                        letter-spacing: 0.3px;
                    }
                }
                .right {
                    .value {
                        display: flex;
                        font-size: 11px;
                        color: #ffffff;
                        letter-spacing: 0.3px;
                    }
                    .text {
                        color: #ffffff;
                        font-size: 11px;
                    }
                }
            }
        }
        .self {
            background-image: url('@/assets/img/tab1/<EMAIL>');
            background-size: 100%;
            width: 375px;
            height: 80px;
            position: fixed;
            left: 0;
            bottom: 0;
            z-index: 1000;
            .me {
                margin-top: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 28px 0 0px;
                .left {
                    display: flex;
                    align-items: center;
                    .user-rank {
                        // .tb();
                        .flex-center();
                        margin-left: 14px;
                        margin-right: 8px;
                        min-width: 30px;
                        font-size: 15px;
                        font-weight: bold;
                        letter-spacing: 0.3px;
                        font-family: HYRUISHOUJ;
                        color: #0f0300;
                    }
                    .avatar {
                        margin-right: 12.28px;
                        width: 40px;
                        height: 40px;
                        background: #7d7d7d;
                        position: relative;
                        border-radius: 50%;
                        .avatar-image {
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            z-index: 50;
                            width: 100%;
                            height: 100%;
                            border-radius: 50%;
                            img {
                                width: 100%;
                                height: 100%;
                                border-radius: 50%;
                            }
                        }
                    }
                    .nickname {
                        .one-line();
                        width: 85px;
                        font-size: 12px;
                        font-weight: bold;
                        color: #0f0300;
                        letter-spacing: 0.3px;
                    }
                }
                .right {
                    .value {
                        .flex-center();
                        flex-direction: column;
                        align-items: flex-end;
                        .num {
                            font-size: 11px;
                            color: #0f0300;
                            letter-spacing: 0.3px;
                        }
                    }
                }
            }
        }
    }
}
.btn {
    background: linear-gradient(0deg, #ffc582 0%, #f2aa77 50%, #fff4bc 100%);
    border: 0.75px solid #fffae0;
    border-radius: 11.5px;
}
</style>
