<template>
    <div class="rank-dates">
        <div
            key="date-total"
            class="date date-total"
            :class="{ active: !currentDate }"
            @click.stop="() => handleSetCurrentDate(totalDate)"
        >
            <span class="date-text">总榜</span>
        </div>
        <div
            ref="$dates"
            class="dates-list"
        >
            <div
                v-for="date in dates"
                :id="`date-${date.originDateText}`"
                ref="$date"
                :key="date.originDateText"
                class="date"
                :class="{ active: currentDate === date.originDateText }"
                @click.stop="() => handleSetCurrentDate(date)"
            >
                <span class="date-text">{{ date.formatText }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { nextTick, onMounted, ref } from 'vue';

const props = defineProps({
    // '20240801'
    currentDate: {
        type: String,
        required: true,
    },
    // ['20240801', '20240802']
    initialDates: {
        type: Array,
        default: () => [],
    },
    // mounted时是否滚动到当前日期
    scrollToCurrentDate: {
        type: Boolean,
        default: true,
    },
    // mounted时是否展示总榜
    showTotalWhenMounted: {
        type: Boolean,
        default: false,
    },
});

const emits = defineEmits(['initCurrentDate', 'updateCurrentDate']);

function getDateTextInfo(d) {
    const originDateText = `${d}`;
    const year = originDateText.slice(0, 4);
    const month = originDateText.slice(4, 6);
    const day = originDateText.slice(6, 8);
    const startTimeText = `${year}/${month}/${day} 00:00:00`;
    const endTimeText = `${year}/${month}/${day} 23:59:59`;
    const startTime = Number.parseInt(new Date(startTimeText).getTime() / 1000, 10);
    const endTime = Number.parseInt(new Date(endTimeText).getTime() / 1000, 10);
    const date = `${year}/${month}/${day}`;
    const formatText = `${month.replace(/^0+/, '')}月${day.replace(/^0+/, '')}日`;

    return {
        originDateText,
        year,
        month,
        day,
        startTimeText,
        endTimeText,
        startTime,
        endTime,
        date,
        formatText,
    };
}

const totalDate = ref({ originDateText: '', text: '总榜' });
const dates = ref(props.initialDates.map(i => getDateTextInfo(i)));
const $dates = ref(null);
const $date = ref(null);

onMounted(() => {
    initDates();
});

function handleSetCurrentDate(info) {
    if (props.currentDate === info.originDateText) {
        return false;
    }
    const now = window.serverTime || Number.parseInt(new Date().getTime() / 1000, 10);
    if (now < info.startTime) {
        showToast('当前日期尚未开启');
        return false;
    }
    emits('updateCurrentDate', { dateText: info.originDateText, info });
    return true;
}

function initDates() {
    if (props.showTotalWhenMounted) {
        emits('initCurrentDate', { dateText: totalDate.value.originDateText, info: totalDate.value });
        return;
    }

    if (myWebview.params.rankPopupDate && props.initialDates.includes(`${myWebview.params.rankPopupDate}`)) {
        const dateText = `${myWebview.params.rankPopupDate}`;
        emits('initCurrentDate', { dateText, info: getDateTextInfo(dateText) });
        if (props.scrollToCurrentDate) {
            scrollToCurrentDate(dateText);
        }
        return;
    }

    const now = window.serverTime || Number.parseInt(new Date().getTime() / 1000, 10);
    let initialDate = totalDate.value;
    for (const date of dates.value) {
        const { startTime, endTime } = date;
        if (now >= startTime && now <= endTime) {
            initialDate = date;
        }
    }
    emits('initCurrentDate', { dateText: initialDate.originDateText, info: initialDate });
    if (props.scrollToCurrentDate) {
        scrollToCurrentDate(initialDate.originDateText);
    }
}

function scrollToCurrentDate(dateText) {
    nextTick(() => {
        if ($dates.value) {
            let currentDateEl = null;
            try {
                currentDateEl = $dates.value.querySelector(`#date-${dateText}`);
            }
            catch {
                currentDateEl = document.getElementById(`date-${dateText}`);
            }
            if (!currentDateEl) {
                return;
            }
            $dates.value.scrollTo(
                currentDateEl.getBoundingClientRect().left
                - $dates.value.getBoundingClientRect().left,
                0,
            );
        }
    });
}
</script>

<style lang="less" scoped>
.rank-dates {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    position: relative;
    z-index: 10;
    .dates-list {
        width: 100%;
        display: flex;
        align-items: center;
        overflow: scroll;
    }
    .date {
        .flex-center();
        position: relative;
        width: 80px;
        height: 22.5px;
        flex-shrink: 0;
        font-size: 12px;
        line-height: 14px;
        font-weight: 400;
        text-align: center;
        color: #ffffff;
        &.active {
            font-size: 14px;
            color: #29090a;
            font-weight: 500;
            width: 69.5px;
            height: 29.5px;
            background-image: url('@/assets/img/tab1/<EMAIL>');
            background-size: 100%;
            .flex-center();
        }
    }
}
</style>
