export const LEVEL_LIST = [
    {
        name: '浮光新贵',
        value: 520000,
        level: 1,
        id: 'card_1',
        rewardList: [
            { id: 'BDG_lv1', num: '5' },
            { id: 'ENT_lv1', num: '5' },
            { id: 'HD_lv1', num: '5' },
            { id: 'PWD_lv1', num: 1 },
            { id: 'AMCN_lv1', num: 1 },
        ],
        bgImg: requireImg('tab2/<EMAIL>'),
        activeBgImg: requireImg('tab2/<EMAIL>'),
    },
    {
        name: '蔚蓝尊者',
        value: 1990000,
        level: 2,
        id: 'card_2',
        rewardList: [
            { id: 'BDG_lv2', num: '10' },
            { id: 'ENT_lv2', num: '10' },
            { id: 'HD_lv2', num: '10' },
            { id: 'PWD_lv2', num: 1 },
            { id: 'AMCN_lv2', num: 1 },
        ],
        bgImg: requireImg('tab2/<EMAIL>'),
        activeBgImg: requireImg('tab2/<EMAIL>'),

    },
    {
        name: '紫曜尊使',
        value: 3000000,
        level: 3,
        id: 'card_3',
        rewardList: [
            { id: 'BDG_lv3', num: '15' },
            { id: 'ENT_lv3', num: '15' },
            { id: 'HD_lv3', num: '15' },
            { id: 'PWD_lv3', num: 1 },
            { id: 'AMCN_lv3', num: 1 },
            { id: 'gift_1314', num: 7 },
        ],
        bgImg: requireImg('tab2/<EMAIL>'),
        activeBgImg: requireImg('tab2/<EMAIL>'),

    },
    {
        name: '赤金名爵',
        value: 5200000,
        level: 4,
        id: 'card_4',
        rewardList: [
            { id: 'BDG_lv4', num: '20' },
            { id: 'ENT_lv4', num: '20' },
            { id: 'HD_lv4', num: '20' },
            { id: 'PWD_lv4', num: 1 },
            { id: 'AMCN_lv4', num: 1 },
            { id: 'gift_3000', num: 7 },
        ],
        bgImg: requireImg('tab2/<EMAIL>'),
        activeBgImg: requireImg('tab2/<EMAIL>'),
    },
    {
        name: '幻金圣尊',
        value: 8880000,
        level: 5,
        id: 'card_5',
        rewardList: [
            { id: 'BDG_lv5', num: '30' },
            { id: 'ENT_lv5', num: '30' },
            { id: 'HD_lv5', num: '30' },
            { id: 'VR_1', num: '15' },
            { id: 'PWD_lv5', num: 1 },
            { id: 'AMCN_lv5', num: 1 },
            { id: 'gift_5000', num: 7 },
        ],
        bgImg: requireImg('tab2/<EMAIL>'),
        activeBgImg: requireImg('tab2/<EMAIL>'),

    },
    {
        name: '传奇神尊',
        value: 13880000,
        level: 6,
        id: 'card_6',
        rewardList: [
            { id: 'BDG_lv6', num: '30' },
            { id: 'ENT_lv6', num: '30' },
            { id: 'HD_lv6', num: '30' },
            { id: 'BDG_X', num: '30' },
            { id: 'VR_2', num: '30' },
            { id: 'PWD_lv6', num: 1 },
            { id: 'AMCN_lv6', num: 1 },
            { id: 'gift_1w', num: 7 },
        ],
        bgImg: requireImg('tab2/<EMAIL>'),
        activeBgImg: requireImg('tab2/<EMAIL>'),
    },
];
