// src/stores/modules/use-encounter-store.js
// 约会卡状态管理Store
import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { LEVEL_LIST } from './config';
import { getRewardDataInfo } from '@/utils';
import { getUserTask as taskApi } from '@/api';
// 配置 dayjs 使用中国时区
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault('Asia/Shanghai');
// 约会卡状态常量
export const DATING_CARD_STATUS = {
    LOCKED: 0, // 待解锁
    UNLOCKED: 1, // 已解锁
};

const PREVIEW_MAP = {
    gift_1314: 'gift_1314.png',
    gift_3000: 'gift_3000.png',
    gift_5000: 'gift_5000.png',
    gift_1w: 'gift_1w.png',
};

// 礼物类型映射 - 基于 LEVEL_LIST 自动生成
export const GIFT_MAPPING = (() => {
    const mapping = {};

    // 从 LEVEL_LIST 中提取所有奖励ID并生成映射
    const allRewardIds = new Set();
    LEVEL_LIST.forEach((level) => {
        level.rewardList.forEach((reward) => {
            allRewardIds.add(reward.id);
        });
    });

    // 为每个奖励ID生成映射配置
    allRewardIds.forEach((id) => {
        mapping[id] = {
            ...getRewardDataInfo(id),
            // 根据ID类型设置标记
            mark: getRewardMark(id),
        };
    });

    return mapping;
})();

// 根据奖励ID获取标记描述
function getRewardMark(id) {
    if (id.startsWith('BDG_'))
        return '个人铭牌';
    if (id.startsWith('ENT_'))
        return '坐骑';
    if (id.startsWith('HD_'))
        return '麦位框';
    if (id.startsWith('PWD_'))
        return '密码红包';
    if (id.startsWith('AMCN_'))
        return '主播昵称';
    if (id.startsWith('VR_'))
        return 'VR礼物';
    if (id.startsWith('gift_'))
        return '礼物使用权';
    return '奖励';
}

// 里程碑数据常量 - 基于 LEVEL_LIST 自动生成
export const MILESTONE_DATA = (() => {
    return LEVEL_LIST.map(level => ({
        value: level.value,
        reward: level.rewardList.map(reward => ({
            ...GIFT_MAPPING[reward.id],
            num: reward.num,
            previewUrl: PREVIEW_MAP[reward.id] || '',
        })),
        cardType: level.level.toString(),
        level: level.level,
        name: level.name,
        levelId: level.id,
    }));
})();

// 约会卡类型映射 - 基于 LEVEL_LIST 自动生成
export const DATING_CARD_MAPPING = (() => {
    const mapping = {};

    LEVEL_LIST.forEach((level) => {
        mapping[level.level] = {
            level: level.level,
            name: level.name,
            bgImg: level.bgImg,
            activeBgImg: level.activeBgImg,
        };
    });

    return mapping;
})();

function initCardStatusList() {
    return LEVEL_LIST.map((level) => {
        return {
            status: 0,
            cardId: '',
            time: 0,
            lv: level.level,
        };
    });
}

export const useEncounterStore = defineStore('encounter', () => {
    // 状态定义
    const currentValue = ref(0); // 当前累计值
    const cardStatusList = ref(initCardStatusList()); // 卡片状态列表

    // 从卡片状态列表中提取完整卡片项的函数
    function extractCardItemMap(list) {
        const object = {};
        list.forEach((item) => {
            object[item.lv] = item;
        });
        return object;
    }

    /**
     * 创建约会卡信息
     * @param {string} cardType - 卡片类型
     * @param {boolean} isCompleted - 是否已完成里程碑
     * @returns {object | null} - 约会卡信息对象
     */
    function getDatingCardInfo(cardType, isCompleted) {
        if (!cardType)
            return null;

        const mapping = DATING_CARD_MAPPING[cardType];
        if (!mapping)
            return null;

        const options = mapping.options || [];
        // 根据里程碑完成状态确定卡片解锁状态
        const status = isCompleted ? DATING_CARD_STATUS.UNLOCKED : DATING_CARD_STATUS.LOCKED;

        return {
            level: cardType,
            name: mapping.name,
            bgImg: mapping.bgImg,
            activeBgImg: mapping.activeBgImg,
            status,
            statusText: status === DATING_CARD_STATUS.UNLOCKED ? '已解锁' : '待解锁',
            unlockTimeStr: cardStatusList.value[mapping.level - 1]?.unlockTimeStr || '',
        };
    }

    // 初始化里程碑状态
    const milestones = computed(() => MILESTONE_DATA.map((item) => {
        const isCompleted = currentValue.value >= item.value;
        const datingCardInfo = getDatingCardInfo(item.cardType, isCompleted);

        return {
            ...item,
            isCompleted,
            datingCardInfo,
            hasSelectedCard: datingCardInfo?.hasSelected,
        };
    }));

    // 下一个里程碑
    const nextMilestone = computed(() => {
        return MILESTONE_DATA.find(item => item.value > currentValue.value) || {};
    });

    // 约会卡列表 - 按等级顺序展示
    const datingCardsList = computed(() => {
        // 从 LEVEL_LIST 动态获取卡片类型数组
        const cardTypes = LEVEL_LIST.map(level => level.level.toString());

        return cardTypes.map((cardType) => {
            // 获取对应卡片类型的里程碑
            const milestone = MILESTONE_DATA.find(
                item => item.cardType === cardType,
            );

            if (!milestone)
                return null;

            // 判断里程碑是否达成
            const isCompleted = currentValue.value >= milestone.value;

            // 使用通用函数获取约会卡信息
            const cardInfo = getDatingCardInfo(cardType, isCompleted);

            // 返回卡片信息，添加里程碑值
            return {
                ...cardInfo,
                milestone: milestone.value,
                isCompleted,
            };
        }).filter(Boolean); // 过滤掉不存在的卡片类型
    });

    // 进度百分比
    const progressPercentage = computed(() => {
        if (!nextMilestone.value)
            return 100;

        // 找到上一个里程碑的值
        const milestoneIndex = MILESTONE_DATA.findIndex(item => item.value === nextMilestone.value.value);
        const prevValue = milestoneIndex > 0 ? MILESTONE_DATA[milestoneIndex - 1].value : 0;

        // 计算进度百分比
        const progress = Math.floor(
            (currentValue.value - prevValue) / (nextMilestone.value.value - prevValue) * 100,
        );

        return Math.min(100, progress);
    });

    /**
     * 获取用户数据
     * @returns {Promise<boolean>} - 是否成功更新数据
     */
    async function update() {
        try {
            // 调用心跳数据接口
            const [response, error] = await taskApi();

            // 如果请求失败或响应码不为0，则返回false
            if (error || response?.code !== 0) {
                console.warn('获取心跳数据失败', error || response?.msg);
                return false;
            }

            const { data } = response;

            // 更新当前累计值
            currentValue.value = data.taskValue || 0;
            cardStatusList.value = (data.cards || []).map(card => ({
                ...card,
                unlockTime: card.time,
                unlockTimeStr: card.time ? dayjs.unix(card.time).tz('Asia/Shanghai').format('MM月DD日HH:mm') : '',
            }));

            return true;
        }
        catch (err) {
            console.error('更新数据异常', err);
            return false;
        }
    }

    return {
        currentValue,
        cardStatusList,
        milestones,
        nextMilestone,
        progressPercentage,
        datingCardsList,
        update,
    };
});
