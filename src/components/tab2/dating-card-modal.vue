<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true">
        <div
            class="dating-card-modal relative box-border h-[367px] w-[375px] pt-39"
        >
            <img
                class="absolute left-1/2 top-[110px] h-[218.5px] w-[375px] -translate-x-1/2"
                src="@/assets/img/tab2/<EMAIL>"
                alt="">
            <div
                ref="cardRef"
                class="card-display relative mx-auto h-[283.5px] w-[208.5px]">
                <img
                    v-if="isCompleted"
                    class="h-full w-full"
                    :src="activeBgImg" />
                <img
                    v-else
                    class="h-full w-full"
                    :src="bgImg" />
                <div
                    v-if="isCompleted"
                    class="">
                    <img
                        class="absolute left-1/2 top-81 h-[71.5px] w-[71.5px] border-[2px] border-[#ffe32a] border-[solid] rounded-full bg-[#525252] -translate-x-1/2"
                        :src="getAvatar(initData.userInfo?.username)"
                        alt="">
                    <div class="absolute left-1/2 top-180 text-center text-[13px] text-[#2C0D00] leading-[13px] -translate-x-1/2">{{ safeOmitTxt(initData.userInfo?.nickname, 7) }}</div>
                </div>
                <div
                    v-if="isCompleted"
                    class="time absolute left-1/2 whitespace-nowrap !bottom-62 -translate-x-1/2">
                    日期：{{ unlockTimeStr || '' }}
                </div>
            </div>

            <div
                v-if="isCompleted"
                class="btns absolute bottom-4 left-1/2 w-full flex items-center justify-center -translate-x-1/2">
                <img
                    v-if="showShare"
                    :src="requireImg('modal/<EMAIL>')"
                    class="mr-26 h-[34px] w-[127.5px]"
                    @click="handleShare"
                />
                <img
                    :src="requireImg('modal/<EMAIL>')"
                    class="h-[34px] w-[127.5px]"
                    @click="handleDownload"
                />
            </div>
        </div>
    </modal-container>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import { DATING_CARD_MAPPING, useEncounterStore } from './use-encounter-store';
import useInitStore from '@/stores/modules/use-init-store';
import { getDomBase64 } from '@/utils';
import { openSharePublishPage, saveImgByBase64, sendImageToIM } from '@/utils/jsbridge';
import useSaveDom from '@/use/use-save-dom';

const showShare = computed(() => myWebview?.params?.os_type !== 'pc');

const cropping = ref(false); // 是否正在裁剪
const cropImagePath = ref(''); // 裁剪后的图片路径

const initStore = useInitStore();

const encounterStore = useEncounterStore();

const { $el: cardRef, downloading, download } = useSaveDom();

const isShow = ref(false);

const level = ref('');
const unlockTimeStr = ref('');
const activeBgImg = ref('');
const bgImg = ref('');
const isCompleted = ref(false);

async function handleShare() {
    if (downloading.value) {
        return;
    }
    const { success, imgPath } = await download();
    if (!success) {
        return;
    }
    if (myWebview.isIOS()) {
        openSharePublishPage({
            attachmentList: [{ type: 1, path: imgPath }],
            pageType: 2,
            subTopicName: '',
        });
    }
    else {
        openSharePublishPage({
            attachmentList: [{ type: 1, path: imgPath }],
            pageType: 5,
            subTopicName: '',
        });
    }
}

async function handleDownload() {
    if (downloading.value) {
        return;
    }
    const { success } = await download();
    if (success) {
        showToast('保存成功');
    }
}

async function save() {
    if (!cardRef.value || cropping.value)
        return;

    try {
        cropping.value = true;
        const cropImg = await getDomBase64(cardRef.value);
        console.log('cropImg', cropImg);
        cropImagePath.value = await saveImgByBase64(cropImg);
        console.log('cropImagePath', cropImagePath.value);
    }
    catch (error) {
        console.error('裁剪卡片失败', error);
    }
    finally {
        cropping.value = false;
    }
}

async function share() {
    if (cropImagePath.value) {
        sendImageToIM(cropImagePath.value);
        return;
    }
    await save();
    sendImageToIM(cropImagePath.value);
}

// 关闭按钮点击处理
function handleClose() {
    isShow.value = false;
}

// 计算属性，获取背景图片
const cardBackground = computed(() => {
    return bgImg.value;
});

// 重置状态
function resetState() {
    level.value = '';
    cropImagePath.value = '';
}

// 监听弹窗关闭，触发检查事件
watch(isShow, (newVal, oldVal) => {
    // if (oldVal && !newVal) {
    //     // 弹窗从显示变为隐藏状态
    //     useEventBus('dating-card-modal-closed').emit();
    // }
});

// 监听事件总线
useEventBus('dating-card-modal').on((payload) => {
    const { show = true, lv = 1 } = payload;

    if (show) {
        level.value = lv;
        unlockTimeStr.value = encounterStore.datingCardsList?.[lv - 1]?.unlockTimeStr || '';
        activeBgImg.value = encounterStore.datingCardsList?.[lv - 1]?.activeBgImg || '';
        bgImg.value = encounterStore.datingCardsList?.[lv - 1]?.bgImg || '';
        isCompleted.value = encounterStore.datingCardsList?.[lv - 1]?.isCompleted || false;
        console.log('unlockTimeStr', unlockTimeStr.value);
    }

    isShow.value = show;

    if (!show) {
        resetState();
    }
});
</script>

<style lang="less" scoped>
.dating-card-modal {
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
}

.nick-name {
    background-repeat: no-repeat;
    background-position: center;
    font-size: 11.5px;
    font-family:
        Alibaba PuHuiTi,
        Alibaba PuHuiTi-Regular;
    font-weight: 400;
    text-align: left;
    color: #ffffff;
    line-height: 13px;
    letter-spacing: -0.23px;
}

.time {
    width: 130px;
    font-size: 11px;
    font-family: FZZhengHeiS-DB-GB, FZZhengHeiS-DB-GB-Regular;
    font-weight: 400;
    text-align: center;
    color: #ffffff;
    line-height: 12px;
    letter-spacing: 0.22px;
}
</style>
