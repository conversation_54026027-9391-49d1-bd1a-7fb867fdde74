<template>
    <div
        class="bg-default relative mt-[-67px] h-[641px] w-[375px] w-full flex flex-col items-center overflow-hidden pt-[40px]"
        :style="`background-image: url(${requireImg('tab2/<EMAIL>')})`"
    >
        <div
            class="bg-default h-[139.5px] w-[375px] flex flex-shrink-0 flex-col items-center pt-[62px]"
            :style="`background-image: url(${requireImg('tab2/<EMAIL>')})`"
        >
            <div
                v-if="!store.nextMilestone?.level"
                class="text-center text-[11px] text-[#FFFFFF]">
                恭喜已达成[传奇神尊]终极身份成就
            </div>
            <div
                v-else
                class="text-center text-[11px] text-[#FFFFFF]">
                当前荣耀值：{{ omitValue(store?.currentValue) }}（1豆=1值 ） 距解锁【{{ store.nextMilestone?.name }}】还差【{{ omitValue(store.nextMilestone?.value - store?.currentValue) }}值】
            </div>
            <div class="mt-[14px] text-[10px] text-[#FFD71F]">送礼（含背包）累计荣耀值，解锁周年限定身份卡&资源牌面</div>
        </div>
        <van-swipe
            ref="swipeRef"
            class="w-375"
            :show-indicators="false"
            :loop="false"
            @change="onSwiperChange">
            <van-swipe-item
                v-for="card in datingCardsList"
                :key="card.level">
                <div class="card-slide box-border overflow-hidden !w-375">
                    <div class="relative h-[290px] w-[100%]">
                        <!-- 卡片背景 -->
                        <img
                            class="absolute left-1/2 top-95 h-[218.5px] w-[375px] -translate-x-1/2"
                            src="@/assets/img/tab2/<EMAIL>"
                            alt="">
                        <!-- 未解锁 -->
                        <div
                            v-if="!card.isCompleted"
                            class="absolute left-1/2 top-0 h-[283.5px] w-[208.5px] -translate-x-1/2">
                            <img
                                :src="card.bgImg"
                                :alt="card?.name"
                                class="h-[283.5px] w-[208.5px]" />
                            <img
                                class="absolute left-1/2 top-155 h-[61px] w-[288px] -translate-x-1/2"
                                src="@/assets/img/tab2/<EMAIL>"
                                alt="">
                            <img
                                class="absolute left-1/2 top-230 h-[44px] w-[150px] -translate-x-1/2"
                                src="@/assets/img/tab2/<EMAIL>"
                                alt=""
                                @click="handleCheckBtnClick(card)">
                            <div
                                class="breath bg-default absolute top-57 h-[100px] w-[41.5px] text-center text-[10px] text-[#FFFFFF] -left-7"
                                :style="`background-image: url(${requireImg('tab2/<EMAIL>')})`"

                            >
                                <div class="shadow-brown mt-[22px] text-[10px] text-[#FFFFFF] leading-[10px]">{{ omitValue(card.milestone, 10000, '') }}<br /> w分</div>
                            </div>
                        </div>
                        <!-- 已解锁 -->

                        <div
                            v-if="card.isCompleted"
                            class="absolute left-1/2 top-0 h-[283.5px] w-[208.5px] -translate-x-1/2"
                        >
                            <img
                                :src="card.activeBgImg"
                                :alt="card?.name"
                                class="h-[283.5px] w-[208.5px]" />
                            <img
                                class="breath absolute top-57 h-[100px] w-[41.5px] -left-7"
                                src="@/assets/img/tab2/<EMAIL>"
                                alt="">

                            <img
                                class="absolute left-1/2 top-81 h-70 w-70 rounded-full -translate-x-1/2"
                                :src="getAvatar(initData.userInfo?.username)"
                                alt="">
                            <div class="absolute left-1/2 top-180 text-center text-[13px] text-[#2C0D00] leading-[13px] -translate-x-1/2">{{ safeOmitTxt(initData.userInfo?.nickname, 7) }}</div>
                            <div class="absolute left-1/2 top-210 whitespace-nowrap text-center text-[10px] text-[#ffffff] leading-[10px] -translate-x-1/2">达成日期：{{ card.unlockTimeStr }}</div>
                            <img
                                class="absolute left-1/2 top-230 h-[44px] w-[150px] -translate-x-1/2"
                                src="@/assets/img/tab2/<EMAIL>"
                                alt=""
                                @click="handleCheckBtnClick(card)">
                        </div>
                    </div>
                </div>
            </van-swipe-item>
        </van-swipe>
        <div
            class="bg-default h-[156.5px] w-[370px] flex flex-col items-center pt-[46px]"
            :style="`background-image: url(${requireImg('tab2/<EMAIL>')})`"
        >
            <broadcast
                class="mx-[2px] w-[360px]"
                direction="x"
                :allow-touch="true"
                :data="store.milestones[currentSwiperIndex]?.reward.reverse() || []">
                <template #default="{ item }">
                    <div class="bg-default mx-[4px] flex flex-shrink-0 flex-col items-center">
                        <div
                            class="bg-default relative h-[69.5px] w-[71px] flex flex-center flex-center"
                            :style="`background-image: url(${requireImg('tab2/<EMAIL>')})`">
                            <img
                                :src="requireImg(item.imageUrl)"
                                class="h-[50px] w-[50px]"
                                alt="">
                            <img
                                v-if="item.previewUrl"
                                class="absolute left-1/2 top-[48px] h-[15.5px] w-[44px] -translate-x-1/2"
                                src="@/assets/img/tab2/<EMAIL>"
                                alt="">
                        </div>
                        <div class="mt-[2px] text-[11px] text-[#FFFFFF] leading-[12px]">{{ item.name }}</div>
                        <div class="text-[11px] text-[#FFFFFF] leading-[12px]">{{ item.mark || '' }}</div>
                    </div>
                </template>
            </broadcast>
        </div>
        <!-- 轮播导航按钮 -->
        <img
            v-if="currentSwiperIndex > 0"
            class="breath-animation absolute left-30 top-285 h-[41.5px] w-[41.5px]"
            :class="{ 'opacity-50': !store.nextMilestone?.level }"
            src="@/assets/img/tab2/<EMAIL>"
            alt="上一张"
            @click="prevSlide" />
        <img
            v-if="currentSwiperIndex < datingCardsList.length - 1"
            class="breath-animation absolute right-30 top-285 h-[41.5px] w-[41.5px]"
            src="@/assets/img/tab2/<EMAIL>"
            alt="下一张"
            @click="nextSlide" />
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useEncounterStore } from './use-encounter-store';
// 轮播组件引用
const swipeRef = ref(null);

// 当前滑块索引
const currentSwiperIndex = ref(0);

// 初始化store
const store = useEncounterStore();
const { datingCardsList } = storeToRefs(store);

/**
 * 轮播切换事件
 * @param {number} index - 当前索引
 */
function onSwiperChange(index) {
    currentSwiperIndex.value = index;
}

/**
 * 上一张
 */
function prevSlide() {
    if (swipeRef.value) {
        swipeRef.value.prev();
    }
}

/**
 * 下一张
 */
function nextSlide() {
    if (swipeRef.value) {
        swipeRef.value.next();
    }
}

// 查看
// 点击查看约会卡
function handleCheckBtnClick(card) {
    useEventBus('dating-card-modal').emit({
        show: true,
        lv: card.level - 1,
    });
}

// 组件挂载时初始化数据
onMounted(async () => {
    await store.update();
    if (store.nextMilestone?.level) {
        currentSwiperIndex.value = store.nextMilestone.level - 1;
        swipeRef.value?.swipeTo(currentSwiperIndex.value);
    }
    else {
        currentSwiperIndex.value = datingCardsList.value.length - 1;
        swipeRef.value?.swipeTo(currentSwiperIndex.value);
    }
});
</script>

<style lang="less" scoped>
.nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    cursor: pointer;

    img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
}

.prev-button {
    left: 10px;
}

.next-button {
    right: 10px;
}

/* 添加呼吸效果动画 */
@keyframes breath {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.9;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 应用呼吸动画 */
.breath-animation {
    animation: breath 2s infinite ease-in-out;
}
.shadow-brown {
    text-shadow:
        1px 1px 0 #9a3e3d,
        -1px -1px 0 #9a3e3d,
        -1px 1px 0 #9a3e3d,
        1px -1px 0 #9a3e3d;
}
</style>
