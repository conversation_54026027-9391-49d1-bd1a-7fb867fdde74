<template>
    <div
        id="status-bar"
        :style="barStyle"></div>
</template>

<script setup>
import { computed, ref } from 'vue';

const barHeight = ref(0);

const barStyle = computed(() => `height:${barHeight.value}px;`);

function create() {
    const { status_height } = parseUrlQuery();
    const dpr = window.devicePixelRatio;
    let statusHeight = 0;
    if (status_height) {
        statusHeight = status_height;
    }
    else {
        try {
            TTJSBridge.invoke('ui', 'setStatusColor', 'white');
            statusHeight = TTJSBridge.invoke('ui', 'getStatusBar');
        }
        catch (error) {
            statusHeight = 0;
        }
    }
    if (window.myWebview.isIOS()) {
        barHeight.value = statusHeight;
    }
    else {
        barHeight.value = Math.floor(statusHeight / dpr);
    }
}

create();
</script>

<style lang="less">
#status-bar {
    width: 100vw;
    position: relative;
    background: transparent;
}
</style>
