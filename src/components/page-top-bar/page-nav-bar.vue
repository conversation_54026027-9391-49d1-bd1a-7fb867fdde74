<template>
    <div
        id="navbar"
        ref="elRef"
        :style="navStyle">
        <img
            :src="requireImg('<EMAIL>')"
            alt="icon"
            class="navbar-left"
            @click.stop="onBack" />
        <div
            class="navbar-title text-[#ffffff]"
            @click="scrollToTarget('overhead', {})">
            <span>
                {{ config.homeTitle }}
            </span>
        </div>
        <img
            :src="requireImg('<EMAIL>')"
            alt=""
            class="navbar-right"
            @click.stop="isShowSection = !isShowSection">
        <div
            v-if="isShowSection"
            class="right-section">
            <!-- <div class="right-section-text right-section-text1"
                @click.stop="toShare">分享</div> -->
            <div
                class="right-section-text"
                @click.stop="refreash">
                刷新
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { onBackPressed } from '@/utils/jsbridge';
import useClickOutside from '@/use/use-clickoutside';
import config from '@/config';

const isIOS = window.myWebview.isIOS();

const isShowSection = ref(false);

const elRef = useClickOutside(() => {
    if (isShowSection.value)
        isShowSection.value = false;
});

const navStyle = computed(() => `height:${isIOS ? 44 : 48}px;`);

function onBack() {
    // 退出webview
    onBackPressed();
}

// 页面跳转到指定的锚点
function scrollToTarget(id, opt = { behavior: 'smooth', block: 'start', inline: 'nearest' }) {
    const target = document.getElementById(id);
    if (target)
        target.scrollIntoView(opt);
}

// 刷新网页
function refreash() {
    window.location.reload();
}
</script>

<style lang="less">
#navbar {
    width: 100vw;
    position: relative;
    line-height: 1.5;
    text-align: center;
    display: flex;
    align-items: center;
    font-size: 14px;
    z-index: 1;
    .navbar {
        &-title {
            max-width: 60%;
            margin: 0 auto;
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
        }
        &-left {
            width: 20px;
            height: 20px;
            margin-left: 12.5px;
        }
        &-right {
            width: 20px;
            height: 20px;
            margin-right: 22px;
        }
    }
    .right-section {
        position: absolute;
        background-image: url('*/top-bar/<EMAIL>');
        background-size: 100% 100%;
        width: 75.5px;
        height: 33.5px;
        right: 6px;
        top: 50px;
        color: #ffffff;
        font-size: 12px;
        &-text {
            height: 32px;
            line-height: 32px;
        }
        &-text1 {
            line-height: 36px;
        }
    }
}
</style>
