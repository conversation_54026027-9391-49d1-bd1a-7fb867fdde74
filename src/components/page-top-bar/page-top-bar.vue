<template>
    <div
        id="statusBar"
        ref="statusBarRef"
        class="bg-transparent transition-all duration-75">
        <!-- StatusBar -->
        <status-bar />

        <!-- Navbar -->
        <page-nav-bar />
    </div>
</template>

<script setup>
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { throttle } from 'lodash';
import statusBar from './status-bar.vue';
import pageNavBar from './page-nav-bar.vue';

const statusBarRef = ref(null);

// 根据滑动距离设置透明度变化
const setBgColor = throttle(() => {
    const targetDom = document.querySelector('#home-content');
    const { scrollTop } = targetDom;
    if (scrollTop > 0) {
        const opacity = scrollTop / 400 >= 1 ? 0.95 : scrollTop / 400;
        statusBarRef.value.style.backgroundColor = `rgba(30, 9, 7, ${opacity})`;
    }
    else {
        statusBarRef.value.style.backgroundColor = 'rgba(30, 9, 7, 0)';
    }
}, 1000 / 60);

onBeforeUnmount(() => {
    const targetDom = document.querySelector('#home-content');
    if (targetDom)
        targetDom.removeEventListener('scroll', setBgColor);
});

onMounted(() => {
    const targetDom = document.querySelector('#home-content');
    targetDom.addEventListener('scroll', setBgColor);
});
</script>

<style lang="less" scoped>
#statusBar {
    background-color: rgba(13, 8, 33, 0);
}
</style>
