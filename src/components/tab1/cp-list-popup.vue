<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="dialog-wrapper">
            <div
                class="bg-default h-[36px] w-[358px] flex flex-center text-[12px] text-[#FCFCFC]"
                :style="`background-image: url(${requireImg('tab1/<EMAIL>')})`"
            >
                赠送 <span class="text-[#F6C400]">帝王套</span>，携带收礼人留下高光印记
            </div>
            <div class="mt-[5px] h-[10px] text-center text-[10px] text-[#ffffff]">（排名不分先后顺序哦~）</div>
            <div
                ref="wrapper"
                class="scroll-wrapper"
                @scroll="handleScroll"
            >
                <div
                    v-for="(item, index) of recordStore.list"
                    :key="index"
                    class="bg-default relative mb-[1px] h-[121px] w-[178px] flex justify-center pt-[25px]"
                    :style="`background-image: url(${currentBg(item.id)})`"
                >
                    <div class="mr-[47px] flex flex-col items-center">
                        <img
                            class="h-[40px] w-[40px] rounded-full bg-[#898989]"
                            :src="getAvatar(item.fromUserInfo.username)"
                            alt="头像"
                        />
                        <div class="mt-[7px] text-[11px] text-[#3F0606]">
                            {{ safeOmitTxt(item.fromUserInfo.nickname, 4) }}
                        </div>
                    </div>
                    <div>
                        <img
                            class="h-[40px] w-[40px] rounded-full bg-[#898989]"
                            :src="getAvatar(item.toUserInfo.username)"
                            alt="头像"
                        />
                        <div class="mt-[7px] text-[11px] text-[#3F0606]">
                            {{ safeOmitTxt(item.toUserInfo.nickname, 4) }}
                        </div>
                    </div>
                    <div class="absolute left-1/2 top-[92px] whitespace-nowrap text-[10px] text-[#ffffff] -translate-x-1/2">高光时间：{{ dayjs.unix(item.time).tz().format('YYYY/MM/DD HH:mm') }}</div>
                </div>
                <div
                    v-if="!recordStore.list?.length"
                    class="mt-[40px] w-[100%] text-center text-[14px] text-[#FFE3BE]"
                >
                    暂无记录
                </div>
            </div>
        </div>
    </popup-container>
</template>

<script setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import { throttle } from 'lodash-es';
import useInviteRecords from './use-invite-records';

const isShow = ref(false);

useEventBus('rule-popup').on(({ show = true }) => {
    isShow.value = show;
});

const wrapper = ref();

const recordStore = useInviteRecords();

const handleScroll = () => {
    const scrollWrap = wrapper.value;
    if (scrollWrap.scrollTop + scrollWrap.clientHeight >= scrollWrap.scrollHeight - 30) {
        recordStore.nextPage();
    }
};

const bgMap = {
    A1: requireImg('tab1/<EMAIL>'),
    A2: requireImg('tab1/<EMAIL>'),
    A3: requireImg('tab1/<EMAIL>'),
    A4: requireImg('tab1/<EMAIL>'),
};

const currentBg = (key) => {
    return bgMap[key] || bgMap.A1;
};

useEventBus('cp-list-popup').on(({ show = true }) => {
    isShow.value = show;
    recordStore.reset();
    recordStore.nextPage();
});
</script>

<style lang="less" scoped>
.dialog-wrapper {
    width: 375px;
    height: 471px;
    background-image: url('@/assets/img/tab1/<EMAIL>');
    background-size: 100% 100%;
    padding-top: 61.5px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .scroll-wrapper {
        width: 100%;
        height: auto;
        flex: 1;
        margin: auto;
        box-sizing: border-box;
        overflow-y: auto;
        overflow-x: hidden;
        display: flex;
        flex-wrap: wrap;

        .record-item {
            width: 100%;
            font-size: 13px;
            font-weight: normal;
            text-align: left;
            color: #ff5f20;
            margin: auto;
            padding: 10px 0 14px 14px;
            display: flex;
            flex-direction: column;
        }
    }
}
</style>
