<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="card-popup relative">
            <!-- 关闭按钮 -->
            <img
                src="@/assets/img/<EMAIL>"
                class="absolute right-7 top-0 h-[34.5px] w-[34.5px]"
                @click="handleClose">
            <div class="mt-[74px] h-[13.5px] text-center text-[12px] text-[#FCFCFC] tracking-[0.48px]">
                送<span class="text-[#FFCC00] font-bold">{{ getRewardDataInfo('A6').name }}</span>({{ getRewardDataInfo('A6').price }}豆)解锁两项真爱限定福利
            </div>

            <div
                class="bg-default relative mt-[16px] h-[191.5px] w-[358.5px]"
                :style="`background-image: url(${requireImg('tab1/<EMAIL>')})`"
            >
            </div>
            <div
                class="bg-default relative mt-[12px] h-[143px] w-[358px]"
                :style="`background-image: url(${requireImg('tab1/<EMAIL>')})`"
            >
                <div class="box-border h-full w-full flex items-center px-[20px]">
                    <XScrollWrapper>
                        <div class="flex">
                            <div class="flex shrink-0 flex-col items-center">
                                <div
                                    class="dating-card relative mx-5 box-border box-border h-[68px] w-[132.5px] flex flex-shrink-0 justify-between px-[7px]"
                                    :style="`background-image: url(${requireImg('tab1/<EMAIL>')})`">
                                    <template
                                        v-for="(reward, rIndex) in CPRewards"
                                        :key="rIndex">
                                        <div class="relative box-border">
                                            <div class="box-border h-[52px] w-[52px] p-2">
                                                <img
                                                    class="h-full w-full object-contain"
                                                    :src="reward?.imageUrl" />
                                            </div>
                                            <div class="reward-text absolute left-1/2 top-[66px] flex items-center justify-center whitespace-nowrap -translate-x-1/2">
                                                {{ reward?.name }}<br />
                                                {{ reward?.mark }}
                                            </div>
                                        </div>
                                    </template>
                                    <div class="tag absolute right-0 top-0 flex items-center justify-center">3天</div>
                                </div>
                            </div>

                            <div
                                v-for="(reward, rIndex) in rewards"
                                :key="rIndex">
                                <div class="milestone-reward relative mx-5 box-border flex-shrink-0">
                                    <div class="relative mx-auto box-border h-[52px] w-[52px] p-2">
                                        <img
                                            class="h-full w-full object-contain"
                                            :src="reward?.imageUrl" />
                                        <div class="reward-text absolute left-1/2 top-[66px] flex items-center justify-center whitespace-nowrap -translate-x-1/2">
                                            {{ reward?.name }}<br />
                                            {{ reward?.mark }}
                                        </div>
                                    </div>
                                    <div class="tag absolute right-0 top-0 flex items-center justify-center">{{ reward?.day }}{{ reward?.unit }}</div>
                                </div>
                            </div>
                        </div>
                    </XScrollWrapper>
                </div>
            </div>
        </div>
    </popup-container>
</template>

<script setup>
import { getRewardDataInfo, getRewardInfo } from '@/utils';

const isShow = ref(false);

// 使用事件总线控制弹窗显示
useEventBus('cp-reward-popup').on(({ show = true }) => {
    isShow.value = show;
});

const CPRewards = [
    { ...getRewardDataInfo('HD_love1'), day: 3, mark: 'CP麦位框' },
    { ...getRewardDataInfo('HD_love2'), day: 3, mark: 'CP麦位框' },
];

const CPRewards2 = [
    //
];

const rewards = [
    { ...getRewardDataInfo('ENT_love'), day: 3, mark: 'CP坐骑' },
    { ...getRewardDataInfo('BDG_love1'), day: 3, mark: 'CP铭牌' },
];

// 关闭弹窗
function handleClose() {
    isShow.value = false;
}

onMounted(() => {
    // isShow.value = true;
});
</script>

<style lang="less" scoped>
.card-popup {
    width: 375px;
    height: 471px;
    max-height: 90vh;
    background: url('@/assets/img/tab1/<EMAIL>');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    // background-color: #fff;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;

    .btn {
        font-size: 12px;
        font-family:
            Alibaba PuHuiTi,
            Alibaba PuHuiTi-Regular;
        font-weight: 400;
        text-align: left;
        color: #ffffff;
        letter-spacing: 0.96px;
    }
}
.dating-card {
    padding-top: 4px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
}

// 里程碑奖励样式
.milestone-reward {
    background-image: url('@/assets/img/tab1/<EMAIL>');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    width: 67px;
    height: 68px;
    padding-top: 4px;
}

.tag {
    width: 30px;
    height: 12px;
    background: linear-gradient(#fbd472 12%, #fff5db 45%, #fbdc72 88%);
    border-radius: 0px 0px 0 9px;
    font-size: 9px;
    font-family:
        Alibaba PuHuiTi,
        Alibaba PuHuiTi-Regular;
    font-weight: 400;
    text-align: center;
    color: #5f260c;
    line-height: 10px;
    letter-spacing: 0.18px;
}

.reward-text {
    font-size: 11px;
    color: #fff9db;
    text-align: center;
}
</style>
