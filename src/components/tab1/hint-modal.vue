<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div
            class="bg-default h-[489.5px] w-[375px] flex flex-col items-center pt-[60px]"
            :style="`background-image: url(${requireImg('tab1/<EMAIL>')})`"
        >
        </div>
    </modal-container>
</template>

<script setup>
import { ref } from 'vue';
import { init } from '@/api';

const props = defineProps({
    type: {
        type: Number,
        default: 0,
    },
});

const isShow = ref(false);

useEventBus('hint-modal').on((params) => {
    isShow.value = params.show;
});
const closeDialog = () => {
    isShow.value = false;
};
</script>

<style lang="less" scoped>

</style>
