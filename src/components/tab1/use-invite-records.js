import { defineStore } from 'pinia';
import { ref } from 'vue';
import { getHighlightMark as listApi } from '@/api/index';

/**
 * 邀请记录
 */
const useHeightRecords = defineStore('heightRecords', () => {
    const list = ref([]);
    let page = 1;
    const SIZE = 20;
    let loading = false;
    let total = Number.MAX_SAFE_INTEGER;
    let hasMore = true;

    const nextPage = async () => {
        if (!hasMore || loading)
            return;
        loading = true;
        try {
            const params = {
                page,
                size: SIZE,
            };
            const [{ code, data }] = await listApi(params);
            if (code !== 0 || params.page !== page)
                return;
            list.value = [...list.value, ...(data.list || [])];
            total = data.total;
            if (list.value.length >= total) {
                hasMore = false;
            }
            page += 1;
        }
        catch (error) {
            console.error(error);
            // 没有数据
        }
        finally {
            loading = false;
        }
    };

    const reset = () => {
        list.value = [];
        loading = false;
        page = 1;
        hasMore = true;
        total = Number.MAX_SAFE_INTEGER;
    };

    return {
        list,
        nextPage,
        reset,
    };
});

export default useHeightRecords;
