/**
 * common-components/rank-list/rank-config.js
 * 排行榜配置常量和工具函数
 */

/**
 * 排行榜类型常量
 */
export const RANK_TYPES = {
    DAILY: 'daily', // 日榜
    WEEKLY: 'weekly', // 周榜
    MONTHLY: 'monthly', // 月榜
    TOTAL: 'total', // 总榜
    REAL_TIME: 'realtime', // 实时榜
};

/**
 * 排行榜状态常量
 */
export const RANK_STATUS = {
    LOADING: 'loading', // 加载中
    SUCCESS: 'success', // 成功
    ERROR: 'error', // 错误
    EMPTY: 'empty', // 空数据
};

/**
 * 排名显示模式
 */
export const RANK_DISPLAY_MODES = {
    NUMBER: 'number', // 数字排名
    ICON: 'icon', // 图标排名
    MIXED: 'mixed', // 混合模式（TOP3图标，其他数字）
};

/**
 * 排行榜预设配置
 */
export const RANK_LIST_PRESETS = {
    // 默认配置
    DEFAULT: {
        pageSize: 20,
        maxItems: 100,
        autoLoad: false,
        enableSelf: true,
        showTop: true,
        showNormal: true,
        showSeparator: false,
        displayMode: RANK_DISPLAY_MODES.MIXED,
    },

    // 简单排行榜配置
    SIMPLE: {
        pageSize: 10,
        maxItems: 50,
        autoLoad: true,
        enableSelf: false,
        showTop: false,
        showNormal: true,
        showSeparator: false,
        displayMode: RANK_DISPLAY_MODES.NUMBER,
    },

    // 完整排行榜配置
    FULL: {
        pageSize: 20,
        maxItems: 200,
        autoLoad: true,
        enableSelf: true,
        showTop: true,
        showNormal: true,
        showSeparator: true,
        displayMode: RANK_DISPLAY_MODES.MIXED,
    },

    // 竞赛排行榜配置
    COMPETITION: {
        pageSize: 50,
        maxItems: 500,
        autoLoad: true,
        enableSelf: true,
        showTop: true,
        showNormal: true,
        showSeparator: true,
        displayMode: RANK_DISPLAY_MODES.ICON,
    },

    // 活动排行榜配置
    ACTIVITY: {
        pageSize: 15,
        maxItems: 100,
        autoLoad: false,
        enableSelf: true,
        showTop: true,
        showNormal: true,
        showSeparator: false,
        displayMode: RANK_DISPLAY_MODES.MIXED,
    },
};

/**
 * TOP排名图标配置
 */
export const TOP_RANK_ICONS = {
    1: {
        icon: '🥇',
        color: '#FFD700',
        bgColor: 'linear-gradient(135deg, #FFD700, #FFA500)',
        textColor: '#FFFFFF',
    },
    2: {
        icon: '🥈',
        color: '#C0C0C0',
        bgColor: 'linear-gradient(135deg, #C0C0C0, #A0A0A0)',
        textColor: '#FFFFFF',
    },
    3: {
        icon: '🥉',
        color: '#CD7F32',
        bgColor: 'linear-gradient(135deg, #CD7F32, #B8860B)',
        textColor: '#FFFFFF',
    },
};

/**
 * 空数据配置
 */
export const EMPTY_STATES = {
    NO_DATA: {
        icon: '🏆',
        text: '暂无数据',
        subText: '快来参与活动吧',
    },
    NO_RANK: {
        icon: '📊',
        text: '暂无上榜',
        subText: '继续努力冲榜',
    },
    LOADING_FAILED: {
        icon: '⚠️',
        text: '加载失败',
        subText: '请稍后重试',
    },
    COMING_SOON: {
        icon: '⏰',
        text: '敬请期待',
        subText: '排行榜即将开启',
    },
};

/**
 * 分隔线文本配置
 */
export const SEPARATOR_TEXTS = {
    TOP3_REWARD: 'TOP3可获得特殊奖励',
    LEVEL_UP: '以上可升级到下一等级',
    PRIZE_POOL: '以上可进入奖池',
    ELIMINATION: '以下将被淘汰',
    CUSTOM: (rank, text) => `TOP${rank}${text}`,
};

/**
 * 创建排行榜配置
 * @param {object} options - 配置选项
 * @returns {object} 完整配置
 */
export function createRankListConfig(options = {}) {
    const {
        preset = 'DEFAULT',
        pageSize,
        maxItems,
        autoLoad,
        enableSelf,
        showTop,
        showNormal,
        showSeparator,
        displayMode,
        ...rest
    } = options;

    const baseConfig = RANK_LIST_PRESETS[preset] || RANK_LIST_PRESETS.DEFAULT;

    return {
        ...baseConfig,
        ...(typeof pageSize === 'number' && { pageSize }),
        ...(typeof maxItems === 'number' && { maxItems }),
        ...(typeof autoLoad === 'boolean' && { autoLoad }),
        ...(typeof enableSelf === 'boolean' && { enableSelf }),
        ...(typeof showTop === 'boolean' && { showTop }),
        ...(typeof showNormal === 'boolean' && { showNormal }),
        ...(typeof showSeparator === 'boolean' && { showSeparator }),
        ...(displayMode && { displayMode }),
        ...rest,
    };
}

/**
 * 获取TOP排名样式
 * @param {number} rank - 排名
 * @returns {object} 样式配置
 */
export function getTopRankStyle(rank) {
    return (
        TOP_RANK_ICONS[rank] || {
            icon: rank.toString(),
            color: '#666666',
            bgColor: '#f0f0f0',
            textColor: '#333333',
        }
    );
}

/**
 * 获取空状态配置
 * @param {string} type - 空状态类型
 * @returns {object} 空状态配置
 */
export function getEmptyState(type = 'NO_DATA') {
    return EMPTY_STATES[type] || EMPTY_STATES.NO_DATA;
}

/**
 * 格式化排名显示
 * @param {number} rank - 排名
 * @param {string} mode - 显示模式
 * @returns {string|object} 格式化后的排名
 */
export function formatRankDisplay(rank, mode = RANK_DISPLAY_MODES.MIXED) {
    if (mode === RANK_DISPLAY_MODES.ICON && rank <= 3) {
        return getTopRankStyle(rank);
    }

    if (mode === RANK_DISPLAY_MODES.MIXED && rank <= 3) {
        return getTopRankStyle(rank);
    }

    return rank.toString();
}

/**
 * 验证排行榜数据格式
 * @param {Array} list - 排行榜数据
 * @returns {boolean} 是否有效
 */
export function validateRankList(list) {
    if (!Array.isArray(list)) {
        return false;
    }

    return list.every((item) => {
        return (
            item
      && typeof item === 'object'
      && typeof item.rank === 'number'
      && item.userInfo
      && typeof item.userInfo === 'object'
        );
    });
}

/**
 * 标准化排行榜数据
 * @param {Array} list - 原始排行榜数据
 * @returns {Array} 标准化后的数据
 */
export function normalizeRankList(list) {
    if (!Array.isArray(list)) {
        return [];
    }

    return list.map((item, index) => ({
        rank: item.rank || index + 1,
        value: item.value || 0,
        userInfo: {
            id: item.userInfo?.id || item.id,
            nickname: item.userInfo?.nickname || item.nickname || '未知用户',
            avatar: item.userInfo?.avatar || item.avatar || '',
            ...item.userInfo,
        },
        ...item,
    }));
}

/**
 * 生成虚位以待数据
 * @param {number} count - 数量
 * @returns {Array} 虚位以待数据
 */
export function generatePlaceholderData(count = 3) {
    return Array.from({ length: count }, (_, index) => ({
        rank: index + 1,
        value: 0,
        isEmpty: true,
        userInfo: {
            id: `placeholder_${index}`,
            nickname: '虚位以待',
            avatar: '',
        },
    }));
}

/**
 * 合并排行榜数据（去重）
 * @param {Array} existingList - 现有数据
 * @param {Array} newList - 新数据
 * @returns {Array} 合并后的数据
 */
export function mergeRankList(existingList, newList) {
    if (!Array.isArray(existingList))
        existingList = [];
    if (!Array.isArray(newList))
        newList = [];

    const existingIds = new Set(
        existingList.map(item => item.userInfo?.id || item.id || item.rank),
    );

    const uniqueNewItems = newList.filter((item) => {
        const itemId = item.userInfo?.id || item.id || item.rank;
        return !existingIds.has(itemId);
    });

    return [...existingList, ...uniqueNewItems];
}

/**
 * 排行榜数据排序
 * @param {Array} list - 排行榜数据
 * @param {string} sortBy - 排序字段
 * @param {string} order - 排序顺序 (asc/desc)
 * @returns {Array} 排序后的数据
 */
export function sortRankList(list, sortBy = 'rank', order = 'asc') {
    if (!Array.isArray(list))
        return [];

    return [...list].sort((a, b) => {
        let aValue = a[sortBy];
        let bValue = b[sortBy];

        // 处理嵌套字段
        if (sortBy.includes('.')) {
            const keys = sortBy.split('.');
            aValue = keys.reduce((obj, key) => obj?.[key], a);
            bValue = keys.reduce((obj, key) => obj?.[key], b);
        }

        if (order === 'desc') {
            return bValue - aValue;
        }
        return aValue - bValue;
    });
}

/**
 * 活动排行榜专用配置
 */
export const ACTIVITY_RANK_CONFIG = createRankListConfig({
    preset: 'ACTIVITY',
    separatorText: SEPARATOR_TEXTS.TOP3_REWARD,
});

/**
 * 竞赛排行榜专用配置
 */
export const COMPETITION_RANK_CONFIG = createRankListConfig({
    preset: 'COMPETITION',
    separatorText: SEPARATOR_TEXTS.LEVEL_UP,
});

/**
 * 简单排行榜专用配置
 */
export const SIMPLE_RANK_CONFIG = createRankListConfig({
    preset: 'SIMPLE',
});

/**
 * 创建活动排行榜配置
 * @param {object} options - 额外配置
 * @returns {object} 活动排行榜配置
 */
export function createActivityRankConfig(options = {}) {
    return createRankListConfig({
        preset: 'ACTIVITY',
        ...options,
    });
}
