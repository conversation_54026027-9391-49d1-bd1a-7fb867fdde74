<template>
    <div class="relative w-[100%] flex flex-col items-center overflow-x-hidden tab-1">
        <DragBtn
            @click="handleDragBtnClick"
        >
            <img
                class="drag-btn h-[56px] w-[55px]"
                src="@/assets/img/tab1/<EMAIL>"
                @click="handleDragBtnClick"

            />
        </DragBtn>
        <div
            class="bg-default relative h-[577px] w-[366.5px] flex flex-col items-center pt-[57px]"
            :style="`background-image: url(${requireImg('tab1/<EMAIL>')})`"
        >
            <div class="text-center text-[12px] text-[#FFFFFF]">赠送礼物达到指定个数升级收藏成就 <br />3个=紫金神话 6个=橙金神话 10个=纯金传说</div>
            <div
                class="bg-default relative mt-[2px] h-[213.5px] w-[338.5px] flex flex-col items-center pt-[52px]"
                :style="`background-image: url(${requireImg('tab1/<EMAIL>')})`"
            >
                <div
                    class="text-center text-[11px] text-[#FFFFFF]"
                    @click="openCpListModal">
                    赠送可携带收礼人画上高光印记，查看>>
                </div>

                <SwiperReward
                    :gift-list="kingGiftList"
                    is-limit
                    @swiper-change="onSwiper"
                    @slide-change="onSlideChange"
                />
            </div>
            <div
                class="bg-default relative mt-[2px] h-[213.5px] w-[338.5px] flex flex-col items-center pt-[52px]"
                :style="`background-image: url(${requireImg('tab1/<EMAIL>')})`"
            >
                <div
                    class="text-center text-[11px] text-[#FFFFFF]"
                >
                    上线时间：7月15日 18:00-7月28日 23:59
                </div>

                <SwiperReward
                    :gift-list="goodGiftList"
                    @swiper-change="onSwiper"
                    @slide-change="onSlideChange"
                />
            </div>
            <img
                class="h-[28px] w-[105px]"
                :src="requireImg('tab1/<EMAIL>')"
                @click="useEventBus('cp-reward-popup').emit({ show: true })">
        </div>
        <div
            class="r-rank-bg pt-[52px]"
        >
            <div
                class="bg-default relative flex flex-col items-center overflow-hidden px-[4px] pt-[14px]"
                :class="isOpen ? 'w-[342.5px] h-[211.5px]' : 'w-[327.5px] h-[104.5px]'"
                :style="`background-image: url(${isOpen ? requireImg('tab1/<EMAIL>') : requireImg('tab1/<EMAIL>')})`"
            >
                <div class="text-center text-[11px] text-[#FFFFFF]">赠送四季帝王套/限定臻品礼物（100豆=1收藏石）</div>
                <div class="mt-[3px] text-center text-[11px] text-[#FFFFFF]">累计收藏石TOP3享红V特权</div>
                <broadcast
                    v-if="isOpen"
                    class="mx-[2px] mt-[12px] w-[300px]"
                    direction="x"
                    :allow-touch="true"
                    :data="rewardList || []">
                    <template #default="{ item }">
                        <div class="bg-default mx-[4px] flex flex-shrink-0 flex-col items-center">
                            <div
                                class="bg-default h-[67.5px] w-[67px] flex flex-center flex-center"
                                :style="`background-image: url(${requireImg('tab1/<EMAIL>')})`">
                                <img
                                    :src="requireImg(item.imageUrl)"
                                    class="h-[50px] w-[50px]"
                                    alt="">
                            </div>
                            <div class="mt-[6px] text-[11px] text-[#FFF9DB] leading-[12px]">{{ item.name }}</div>
                            <div class="text-[11px] text-[#FFF9DB] leading-[12px]">{{ item.mark || '' }}</div>
                        </div>
                    </template>
                </broadcast>
                <div
                    class="btn absolute bottom-[10px] right-1/2 h-[23px] flex translate-x-1/2 items-center justify-center border-[0.75px] border-[#fffae0] border-[solid] rounded-[11.5px] px-[10px] text-[12px] text-[#4C0000] font-bold"
                    @click="isOpen = !isOpen"
                >
                    {{ isOpen ? '收起' : '展开' }}奖励
                </div>
            </div>
            <div
                class="rankTop mt-[-80px]"
            >
                <van-list
                    v-model:loading="loading"
                    :finished="!rankStore.hasMore"
                    offset="100"
                    @load="handleLoadMore"
                >
                    <RankList
                        :list="rankStore.list"
                        :self-data="rankStore.selfData"
                        :show-top="true"
                        :show-normal="true"
                        :show-self="!!rankStore.selfData?.userInfo?.nickname"
                        :top-component="RankTop"
                        :normal-component="RankItem"
                        :self-component="RankItem"
                        :normal-component-props="{
                            valueLabel: '收藏石',
                        }"
                        :self-component-props="{
                            valueLabel: '收藏石',
                        }"

                    >
                    </RankList>
                </van-list>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { createRankListStore } from './rank-list/use-rank-list.js';
import RankTop from './rank-top/rank-top.vue';
import RankItem from './rank-item/rank-item.vue';
import SwiperReward from './swiper-reward.vue';
import { getUserGift as giftApi, getBlackGoldRank as rankApi } from '@/api';
import { scrollToAnchor } from '@/utils';

const isOpen = ref(false);

const refreshing = ref(false);
const loading = ref(false);
const rankRef = ref(null);

// 创建排行榜store
const rankStore = createRankListStore('BlackGoldRankStore', {
    apiFunction: rankApi,
    pageSize: 20,
    maxItems: 200,
})();

const rewardList = [
    { ...getRewardInfo('BDG_sc') },
    { ...getRewardInfo('ENT_sc') },
    { ...getRewardInfo('HD_sc') },
    { ...getRewardInfo('VR_sc'), name: '红V认证', mark: '' },
];

const openCpListModal = () => {
    useEventBus('cp-list-popup').emit({ show: true });
};

// 处理拖拽按钮点击事件
const handleDragBtnClick = () => {
    useEventBus('hint-modal').emit({ show: true });
};

// 加载更多
const handleLoadMore = async () => {
    if (rankStore.isLoading || !rankStore.hasMore) {
        loading.value = false;
        return;
    }
    try {
        await rankStore.loadMore({
            uid: myWebview.params.uid,
        });
    }
    finally {
        loading.value = false;
    }
};

// 刷新排行榜
const handleRefresh = async () => {
    try {
        await rankStore.loadData({
            uid: myWebview.params.uid,
            page: 1,
            size: 20,
        });
    }
    finally {
        refreshing.value = false;
    }
};

const kingGiftList = ref([{ id: 'A1', num: 0 }, { id: 'A2', num: 0 }, { id: 'A3', num: 0 }, { id: 'A4', num: 0 }]);
const goodGiftList = ref([{ id: 'A5', num: 0 }, { id: 'A6', num: 0 }, { id: 'A7', num: 0 }, { id: 'A8', num: 0 }]);

async function getGiftList(params) {
    const [{ code, data }] = await giftApi(params);
    if (code === 0) {
        kingGiftList.value = data.kingGiftList;
        goodGiftList.value = data.goodGiftList;
        return;
    }
}

onMounted(async () => {
    getGiftList();
    handleRefresh();
    if (!localStorage.getItem('hint-modal') === '1') {
        useEventBus('hint-modal').emit({ show: true });
        localStorage.setItem('hint-modal', '1');
    }
    if (myWebview.params.showRank) {
        rankRef.value?.scrollIntoView({ behavior: 'smooth' });
        scrollToAnchor('.tab-1', '.rankTop');
    }
});

// Swiper 事件处理方法（如果需要在父组件中处理）
const onSwiper = (_swiper) => {
    // 可以在这里处理 swiper 实例
};

const onSlideChange = (_swiper) => {
    // 可以在这里处理幻灯片切换事件
};
</script>

<style scoped>
.r-rank-bg {
    height: auto;
    width: 375px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    background: url('@/assets/img/tab1/<EMAIL>') no-repeat center top;
    background-size: 375px 269px;
}

.btn {
    background: linear-gradient(0deg, #ffc582 0%, #f2aa77 50%, #fff4bc 100%);
    border: 0.75px solid #fffae0;
    border-radius: 11.5px;
}
</style>
