<template>
    <van-popup
        :show="isShow"
        :close-on-click-overlay="true"
        :closeable="false"
        class="modal-center !bg-transparent"
        @close="close"
    >
        <div class="modal-cp-card !bg-transparent">
            <div
                v-show="!loading"
                ref="$el"
                class="content !bg-transparent">
                <img
                    class="bg"
                    :src="requireImg(`${bgImage}`)"
                    alt="">
                <div
                    class="avatar left"
                    @click.stop="() => to<PERSON>erson(baseStore.myUserInfo?.username)">
                    <img
                        :src="requireImg('modal/<EMAIL>')"
                        class="avatar-wrapper">
                    <div class="avatar-image">
                        <img
                            v-if="cp.left.avatarBase64"
                            :src="cp.left.avatarBase64"
                            alt=""
                            @error="avatarError">
                        <img
                            v-else
                            :src="requireImg('default_avatar_no_compress.png')"
                            alt="">
                    </div>
                </div>
                <div
                    class="avatar right"
                    @click.stop="() => toPerson(cpStore.currentShownCp?.userInfo?.username)">
                    <img
                        :src="requireImg('modal/<EMAIL>')"
                        class="avatar-wrapper">
                    <div class="avatar-image">
                        <img
                            v-if="cp.right.avatarBase64"
                            :src="cp?.right?.avatarBase64"
                            alt=""
                            @error="avatarError">
                        <img
                            v-else
                            :src="requireImg('default_avatar_no_compress.png')"
                            alt="">
                    </div>
                </div>
                <div class="time">日期：{{ timeText }}</div>
            </div>
        </div>
        <div class="mt-[25px] flex items-end justify-center">
            <div
                v-if="showShare"
                class="share mr-[26px]"
                @click="handleShare"></div>
            <div
                class="download"
                :class="{ center: !showShare }"
                @click="handleDownload"></div>
        </div>
    </van-popup>
</template>

<script setup>
import dayjs from 'dayjs';
import useLoading from '@/hooks/useLoading';
import { getAvatarBase64 } from '@/utils/self';
import { openSharePublishPage } from '@/utils/jsbridge';
import useSaveDom from '@/use/use-save-dom';

const { $el, downloading, download } = useSaveDom();

const loading = ref(false);
const timeText = ref('');
const cp = ref({
    left: { uid: 0, username: '', nickname: '', avatarBase64: '' },
    right: { uid: 0, username: '', nickname: '', avatarBase64: '' },
});

const params = computed(() => myWebview.params);
const isShow = ref(false);
const level = computed(() => +params.value.cardLv);
const shownLeftNickname = computed(() => sliceWithEmoji(cp.value.left.nickname));
const shownRightNickname = computed(() => sliceWithEmoji(cp.value.right.nickname));
const showShare = computed(() => myWebview.params.os_type !== 'pc');
const bgImage = computed(() => {
    if (level.value === 1) {
        return `modal/<EMAIL>`;
    }
    return `modal/<EMAIL>`;
});

useEventBus('modal-cp-card').on(({ show = true }) => {
    isShow.value = show;
});

useLoading(computed(() => loading.value || downloading.value));

watch(() => isShow.value, async (val) => {
    if (val) {
        await init();
    }
    else {
        setTimeout(() => {
            cp.value = {
                left: { uid: 0, username: '', nickname: '', avatarBase64: '' },
                right: { uid: 0, username: '', nickname: '', avatarBase64: '' },
            };
            timeText.value = '';
        }, 500);
    }
});

async function init() {
    loading.value = true;
    if (params.value.date) {
        timeText.value = dayjs.unix(+params.value.date).tzFormat('YYYY年MM月DD日');
    }
    if (params.value.leftUsername) {
        cp.value.left.uid = params.value.leftUid;
        cp.value.left.username = params.value.leftUsername;
        cp.value.left.nickname = params.value.leftNickname;
        cp.value.right.uid = params.value.rightUid;
        cp.value.right.username = params.value.rightUsername;
        cp.value.right.nickname = params.value.rightNickname;
    }

    await Promise.all([
        getAvatarBase64(cp.value.left.username),
        getAvatarBase64(cp.value.right.username),
    ]).then(([leftAvatarBase64, rightAvatarBase64]) => {
        cp.value.left.avatarBase64 = leftAvatarBase64;
        cp.value.right.avatarBase64 = rightAvatarBase64;
    }).catch(() => {
        cp.value.left.avatarBase64 = '';
        cp.value.right.avatarBase64 = '';
    });
    loading.value = false;
}

async function handleShare() {
    if (downloading.value) {
        return;
    }
    const { success, imgPath } = await download();
    if (!success) {
        return;
    }
    if (myWebview.isIOS()) {
        openSharePublishPage({
            attachmentList: [{ type: 1, path: imgPath }],
            pageType: 2,
            subTopicName: '',
        });
    }
    else {
        openSharePublishPage({
            attachmentList: [{ type: 1, path: imgPath }],
            pageType: 5,
            subTopicName: '',
        });
    }
}

async function handleDownload() {
    if (downloading.value) {
        return;
    }
    const { success } = await download();
    if (success) {
        showToast('保存成功');
    }
}

function sliceWithEmoji(input, length = 6) {
    const chars = Array.from(input);
    return chars.length > length ? `${chars.slice(0, length).join('')}...` : chars.slice(0, length).join('');
}

function close() {
    isShow.value = false;
}
</script>

<style lang="less" scoped>
.modal-center {
    width: 100% !important;
    max-width: 100% !important;
    background: transparent !important;
}

.modal-cp-card {
    width: 308px;
    height: 235px;
    position: relative;
    margin: 0 auto;
    background: transparent !important;
    .content {
        background: transparent !important;
        position: relative;
        width: 308px;
        height: 235px;
        .bg {
            width: 308px;
            height: 181px;
            position: absolute;
            left: 0;
            top: 54px;
        }
        .time {
            // background-image: url('@/assets/img/zak/<EMAIL>');
            // background-size: 100% 100%;
            // background-repeat: no-repeat;
            .left-center();
            color: #ffffff;
            bottom: 14px;
            // width: 167px;
            // height: 37px;
            font-size: 12px;
            letter-spacing: 0.3px;
            display: flex;
            justify-content: center;
            align-items: center;
            .text-shadow-gray();
        }
        .avatar {
            position: absolute;
            top: 0px;
            width: 97.5px;
            height: 91.5px;
            z-index: 20;
            &.left {
                left: 37px;
            }
            &.right {
                right: 47px;
            }
            .avatar-wrapper {
                position: absolute;
                left: 0;
                top: 0;
                width: 97.5px;
                height: 91.5px;
                z-index: 10;
            }
            .avatar-image {
                position: absolute;
                left: 27px;
                top: 22px;
                width: 59.5px;
                height: 59px;
                border-radius: 50%;
                z-index: 9;
                img {
                    width: 58px;
                    height: 58px;
                    border-radius: 50%;
                }
            }
        }
        .nickname {
            position: absolute;
            top: 475px;
            width: 114px;
            height: 32.5px;
            img {
                width: 100%;
                height: 100%;
                position: absolute;
                left: 0;
                top: 0;
            }
            &.left {
                left: 60px;
            }
            &.right {
                right: 60px;
            }
        }
    }
}
.download {
    .pic-bg(url('@/assets/img/modal/<EMAIL>'), 128px, 34px);
}
.share {
    .pic-bg(url('@/assets/img/modal/<EMAIL>'), 128px, 34px);
}
</style>
