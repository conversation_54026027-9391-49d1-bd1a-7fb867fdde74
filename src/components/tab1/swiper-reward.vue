<!-- src/components/tab1/swiper-reward.vue -->
<template>
    <Swiper
        class="mt-[4px] h-[132px] w-[338.5px]"
        :slides-per-view="3"
        :centered-slides="true"
        :modules="[Autoplay]"
        :loop="!isLimit"
        :autoplay="{ delay: 5000, disableOnInteraction: false }"
        @swiper="onSwiper"
        @slide-change="onSlideChange"
    >
        <SwiperSlide
            v-for="(item, index) of giftList"
            :key="index">
            <div
                class="bg-default relative h-[133px] w-[132px]"
                :style="`background-image: url(${getStatusBg(item)})`"
            >
                <img
                    class="absolute left-1/2 top-[12px] h-[56px] w-[56px] -translate-x-1/2"
                    :src="getRewardInfo(item.id).imageUrl"
                    alt="">

                <div
                    v-if="index === currentIndex && isLimit"
                    class="bg-default absolute right-[-17px] top-0 h-17 w-86 flex items-center justify-center text-[9px] text-[#FFFFFF]"
                    :style="`background-image: url(${requireImg('tab1/<EMAIL>')})`"
                >
                    <span v-if="store.serverTime >= item.onLineTime">{{ dayjs.unix(item.onLineTime).tz().format('MM月DD日 HH:mm') }}上线</span>
                    <span v-else-if="store.serverTime < item.offLineTime"> {{ dayjs.unix(item.offLineTime).tz().format('MM月DD日 HH:mm') }}下线</span>
                    <span v-else>已下线</span>
                </div>

                <div class="absolute left-1/2 top-[78px] text-[12px] text-[#FFFFFF] -translate-x-1/2">已送{{ item.num }}个</div>

                <div
                    class="bg-default text-shadow-gray absolute left-1/2 top-[100px] h-[33px] w-[95px] flex flex-col items-center whitespace-nowrap pt-[3px] text-[12px] text-[#FFFFFF] -translate-x-1/2"
                    :style="`background-image: url(${requireImg('tab1/<EMAIL>')})`"
                >
                    {{ getRewardInfo(item.id).name }}
                    <div>{{ omitValue(getRewardInfo(item.id).price) }}豆</div>
                </div>
            </div>
        </SwiperSlide>
    </Swiper>
</template>

<script setup>
import { ref } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Autoplay } from 'swiper/modules';
import dayjs from 'dayjs';
import useInitStore from '@/stores/modules/use-init-store.js';

// 导入 Swiper 样式
import 'swiper/css';

// Props 定义
const props = defineProps({
    giftList: {
        type: Array,
        default: () => [],
    },
    isLimit: {
        type: Boolean,
        default: false,
    },
});

// Emit 定义
const emit = defineEmits(['swiperChange', 'slideChange']);

const getStatusBg = (item) => {
    if (item.num >= 8) {
        return requireImg('tab1/<EMAIL>'); //
    }
    else if (item.num >= 5) {
        return requireImg('tab1/<EMAIL>'); //
    }
    else if (item.num >= 3) {
        return requireImg('tab1/<EMAIL>'); //
    }
    else {
        return requireImg('tab1/<EMAIL>'); //
    }
};

// 响应式数据
const currentIndex = ref(0);
const mySwiper = ref(null);

// Store
const store = useInitStore();

// 更新幻灯片样式的方法
const updateSlideStyles = () => {
    if (!mySwiper.value)
        return;

    const slides = mySwiper.value.slides;
    const activeIndex = mySwiper.value.activeIndex;

    slides.forEach((slide, index) => {
        const slideElement = slide;

        // 重置所有幻灯片的样式
        slideElement.style.transform = 'scale(0.8)'; // 默认缩小
        slideElement.style.opacity = '0.7';
        slideElement.style.transition = 'transform 0.3s ease, opacity 0.3s ease';

        if (index === activeIndex) {
            // 当前激活的幻灯片
            slideElement.style.transform = 'scale(1)'; // 恢复正常大小
            slideElement.style.opacity = '1';
        }
        else if (index === activeIndex + 1 || index === activeIndex - 1) {
            // 左右相邻的幻灯片可以稍微大一点，或者调整透明度
            slideElement.style.transform = 'scale(0.9)';
            slideElement.style.opacity = '0.85';
        }
    // 其他非相邻的幻灯片保持更小或更透明的状态
    });
};

// Swiper 事件处理
const onSwiper = (swiper) => {
    mySwiper.value = swiper;
    currentIndex.value = swiper.activeIndex;
    updateSlideStyles(); // 初始化时更新一次样式
    emit('swiperChange', swiper);
};

const onSlideChange = (swiper) => {
    currentIndex.value = swiper.activeIndex;
    updateSlideStyles(); // 幻灯片切换时更新样式
    emit('slideChange', swiper);
};
</script>

<style scoped>
.swiper-slide {
    width: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box; /* 确保 padding 和 border 不会增加元素宽度 */
    transition:
        transform 0.3s ease,
        opacity 0.3s ease; /* 添加过渡效果 */
    /* 初始状态：两边小 */
    transform: scale(0.8);
    opacity: 0.7;
}
</style>
