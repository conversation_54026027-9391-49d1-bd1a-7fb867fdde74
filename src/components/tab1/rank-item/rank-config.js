/**
 * common-components/rank-item/rank-config.js
 * 排行榜项目配置常量和工具函数
 */

/**
 * 排行榜项目主题
 */
export const RANK_ITEM_THEMES = {
    DEFAULT: 'default',
    DARK: 'dark',
    LIGHT: 'light',
    COLORFUL: 'colorful',
};

/**
 * TOP排名图标配置
 */
export const TOP_RANK_ICONS = {
    STYLE_1: {
        1: '/rank-icons/crown-gold.png',
        2: '/rank-icons/crown-silver.png',
        3: '/rank-icons/crown-bronze.png',
    },
    STYLE_2: {
        1: '/rank-icons/medal-gold.png',
        2: '/rank-icons/medal-silver.png',
        3: '/rank-icons/medal-bronze.png',
    },
    STYLE_3: {
        1: '/rank-icons/trophy-gold.png',
        2: '/rank-icons/trophy-silver.png',
        3: '/rank-icons/trophy-bronze.png',
    },
    EMOJI: {
        1: '🥇',
        2: '🥈',
        3: '🥉',
    },
};

/**
 * 排行榜项目预设配置
 */
export const RANK_ITEM_PRESETS = {
    // 默认配置
    DEFAULT: {
        theme: RANK_ITEM_THEMES.DEFAULT,
        clickable: true,
        showRoomStatus: true,
        showExtraInfo: false,
        showDiff: false,
        topRankIcons: TOP_RANK_ICONS.STYLE_1,
    },

    // 简洁配置
    SIMPLE: {
        theme: RANK_ITEM_THEMES.LIGHT,
        clickable: false,
        showRoomStatus: false,
        showExtraInfo: false,
        showDiff: false,
        topRankIcons: TOP_RANK_ICONS.EMOJI,
    },

    // 完整配置
    FULL: {
        theme: RANK_ITEM_THEMES.DEFAULT,
        clickable: true,
        showRoomStatus: true,
        showExtraInfo: true,
        showDiff: true,
        topRankIcons: TOP_RANK_ICONS.STYLE_2,
    },

    // 个人排名配置
    SELF: {
        theme: RANK_ITEM_THEMES.DEFAULT,
        clickable: false,
        showRoomStatus: false,
        showExtraInfo: true,
        showDiff: true,
        topRankIcons: TOP_RANK_ICONS.STYLE_1,
    },

    // 竞赛配置
    COMPETITION: {
        theme: RANK_ITEM_THEMES.COLORFUL,
        clickable: true,
        showRoomStatus: true,
        showExtraInfo: true,
        showDiff: false,
        topRankIcons: TOP_RANK_ICONS.STYLE_3,
    },
};

/**
 * 数值格式化类型
 */
export const VALUE_FORMAT_TYPES = {
    NONE: 'none', // 不格式化
    THOUSAND: 'thousand', // 千分位
    UNIT: 'unit', // 单位缩写 (K, M, B)
    CHINESE: 'chinese', // 中文单位 (万, 亿)
    CUSTOM: 'custom', // 自定义格式化
};

/**
 * 昵称截断配置
 */
export const NICKNAME_TRUNCATE = {
    SHORT: 6, // 短昵称
    MEDIUM: 10, // 中等昵称
    LONG: 15, // 长昵称
    FULL: 0, // 不截断
};

/**
 * 头像尺寸配置
 */
export const AVATAR_SIZES = {
    SMALL: {
        width: 32,
        height: 32,
        top3Width: 36,
        top3Height: 36,
    },
    MEDIUM: {
        width: 40,
        height: 40,
        top3Width: 44,
        top3Height: 44,
    },
    LARGE: {
        width: 48,
        height: 48,
        top3Width: 52,
        top3Height: 52,
    },
};

/**
 * 创建排行榜项目配置
 * @param {object} options - 配置选项
 * @returns {object} 完整配置
 */
export function createRankItemConfig(options = {}) {
    const {
        preset = 'DEFAULT',
        theme,
        clickable,
        showRoomStatus,
        showExtraInfo,
        showDiff,
        topRankIcons,
        avatarSize = 'MEDIUM',
        nicknameLength = NICKNAME_TRUNCATE.MEDIUM,
        valueFormat = VALUE_FORMAT_TYPES.UNIT,
        ...rest
    } = options;

    const baseConfig = RANK_ITEM_PRESETS[preset] || RANK_ITEM_PRESETS.DEFAULT;

    return {
        ...baseConfig,
        ...(theme && { theme }),
        ...(typeof clickable === 'boolean' && { clickable }),
        ...(typeof showRoomStatus === 'boolean' && { showRoomStatus }),
        ...(typeof showExtraInfo === 'boolean' && { showExtraInfo }),
        ...(typeof showDiff === 'boolean' && { showDiff }),
        ...(topRankIcons && { topRankIcons }),
        avatarSize: AVATAR_SIZES[avatarSize] || AVATAR_SIZES.MEDIUM,
        nicknameLength,
        valueFormat,
        ...rest,
    };
}

/**
 * 格式化数值
 * @param {number} value - 原始数值
 * @param {string} type - 格式化类型
 * @param {Function} customFormatter - 自定义格式化函数
 * @returns {string} 格式化后的数值
 */
export function formatValue(value, type = VALUE_FORMAT_TYPES.UNIT, customFormatter = null) {
    if (typeof value !== 'number' || Number.isNaN(value)) {
        return '0';
    }

    // 自定义格式化
    if (type === VALUE_FORMAT_TYPES.CUSTOM && typeof customFormatter === 'function') {
        return customFormatter(value);
    }

    // 使用全局格式化函数
    if (typeof window.omitValue === 'function') {
        return window.omitValue(value);
    }

    switch (type) {
        case VALUE_FORMAT_TYPES.NONE:
            return value.toString();

        case VALUE_FORMAT_TYPES.THOUSAND:
            return value.toLocaleString();

        case VALUE_FORMAT_TYPES.UNIT:
            if (value >= 1000000000) {
                return `${(value / 1000000000).toFixed(1)}B`;
            }
            else if (value >= 1000000) {
                return `${(value / 1000000).toFixed(1)}M`;
            }
            else if (value >= 1000) {
                return `${(value / 1000).toFixed(1)}K`;
            }
            return value.toString();

        case VALUE_FORMAT_TYPES.CHINESE:
            if (value >= 100000000) {
                return `${(value / 100000000).toFixed(1)}亿`;
            }
            else if (value >= 10000) {
                return `${(value / 10000).toFixed(1)}万`;
            }
            return value.toString();

        default:
            return value.toString();
    }
}

/**
 * 截断昵称
 * @param {string} nickname - 原始昵称
 * @param {number} maxLength - 最大长度
 * @returns {string} 截断后的昵称
 */
export function truncateNickname(nickname, maxLength = NICKNAME_TRUNCATE.MEDIUM) {
    if (!nickname || typeof nickname !== 'string') {
        return '未知用户';
    }

    if (maxLength === 0 || nickname.length <= maxLength) {
        return nickname;
    }

    // 使用全局文本截断函数
    if (typeof window.omitTxt === 'function') {
        return window.omitTxt(nickname, maxLength);
    }

    return `${nickname.substring(0, maxLength)}...`;
}

/**
 * 获取头像URL
 * @param {object} userInfo - 用户信息
 * @returns {string} 头像URL
 */
export function getAvatarUrl(userInfo) {
    if (!userInfo)
        return '';

    return getAvatar(userInfo.username || userInfo.id);
}

/**
 * 获取排名显示文本
 * @param {number} rank - 排名
 * @param {object} data - 排名数据
 * @returns {string} 排名显示文本
 */
export function getRankDisplay(rank, data = {}) {
    if (rank === 0)
        return '';

    // 优先使用数据中的人性化排名
    if (data.rankHuman) {
        return data.rankHuman;
    }

    // 处理超出范围的排名
    if (rank > 99) {
        return '99+';
    }

    return rank.toString();
}

/**
 * 获取TOP排名图标
 * @param {number} rank - 排名
 * @param {object} iconSet - 图标集合
 * @returns {string} 图标URL或emoji
 */
export function getTopRankIcon(rank, iconSet = TOP_RANK_ICONS.STYLE_1) {
    if (rank < 1 || rank > 3)
        return '';
    return iconSet[rank] || '';
}

/**
 * 验证排行榜项目数据
 * @param {object} data - 排名数据
 * @returns {boolean} 是否有效
 */
export function validateRankItemData(data) {
    if (!data || typeof data !== 'object') {
        return false;
    }

    // 检查必要字段
    const hasRank = typeof data.rank === 'number' && data.rank > 0;
    const hasUserInfo = data.userInfo && typeof data.userInfo === 'object';
    const hasValue = typeof data.value === 'number';

    return hasRank && hasUserInfo && hasValue;
}

/**
 * 标准化排行榜项目数据
 * @param {object} data - 原始数据
 * @param {number} index - 索引（用于生成排名）
 * @returns {object} 标准化后的数据
 */
export function normalizeRankItemData(data, index = 0) {
    if (!data)
        return null;

    return {
        rank: data.rank || index + 1,
        value: data.value || 0,
        userInfo: {
            id: data.userInfo?.id || data.id,
            username: data.userInfo?.username || data.username,
            nickname: data.userInfo?.nickname || data.nickname || '未知用户',
            avatar: data.userInfo?.avatar || data.avatar || '',
            ...data.userInfo,
        },
        channelInfo: data.channelInfo || {},
        extraInfo: data.extraInfo || '',
        rankHuman: data.rankHuman || '',
        ltPrevValueHuman: data.ltPrevValueHuman || '',
        gtNextValueHuman: data.gtNextValueHuman || '',
        ...data,
    };
}

/**
 * 生成虚位以待数据
 * @param {number} rank - 排名
 * @returns {object} 虚位以待数据
 */
export function generatePlaceholderItem(rank) {
    return {
        rank,
        value: 0,
        isEmpty: true,
        userInfo: {
            id: `placeholder_${rank}`,
            username: '',
            nickname: '虚位以待',
            avatar: '',
        },
        channelInfo: {},
        extraInfo: '',
        rankHuman: rank.toString(),
    };
}

/**
 * 计算差距信息
 * @param {object} currentItem - 当前项目
 * @param {object} prevItem - 上一名项目
 * @param {object} nextItem - 下一名项目
 * @returns {object} 差距信息
 */
export function calculateDiffInfo(currentItem, prevItem = null, nextItem = null) {
    const diffInfo = {};

    if (prevItem && prevItem.value > currentItem.value) {
        const diff = prevItem.value - currentItem.value;
        diffInfo.ltPrev = `距上差: ${formatValue(diff)}`;
    }

    if (nextItem && currentItem.value > nextItem.value) {
        const diff = currentItem.value - nextItem.value;
        diffInfo.gtNext = `领先: ${formatValue(diff)}`;
    }

    return diffInfo;
}

/**
 * 活动排行榜项目配置
 */
export const ACTIVITY_RANK_ITEM_CONFIG = createRankItemConfig({
    preset: 'FULL',
    theme: RANK_ITEM_THEMES.DEFAULT,
    topRankIcons: TOP_RANK_ICONS.STYLE_2,
});

/**
 * 简单排行榜项目配置
 */
export const SIMPLE_RANK_ITEM_CONFIG = createRankItemConfig({
    preset: 'SIMPLE',
    avatarSize: 'SMALL',
    nicknameLength: NICKNAME_TRUNCATE.SHORT,
});

/**
 * 个人排名项目配置
 */
export const SELF_RANK_ITEM_CONFIG = createRankItemConfig({
    preset: 'SELF',
    showDiff: true,
    showExtraInfo: true,
});
