<!--
  common-components/rank-top/rank-top.vue
  TOP排名展示组件 - 提供TOP3特殊样式展示，支持不同排名的背景和装饰
-->
<template>
    <div
        class="rank-top"
        :class="[
            containerClass,
            `rank-${rank}`,
            themeClass,
            {
                'is-empty': isEmpty,
                'is-clickable': clickable,
                'has-crown': showCrown,
                'has-decoration': showDecoration,
            },
        ]"
        :style="[containerStyle]"
        @click="handleClick">
        <!-- 背景装饰 -->
        <img
            v-if="showDecoration && backgroundImage"
            class="rank-background"
            :class="backgroundClass"
            :src="backgroundImage"
            alt="">

        <!-- 皇冠装饰 -->
        <div
            v-if="showCrown && crownImage"
            class="rank-crown"
            :class="crownClass"
            :style="{ backgroundImage: `url(${crownImage})` }">
            <slot
                name="crown"
                :rank="rank"
                :crown-image="crownImage" />
        </div>

        <!-- 排名数字/图标 -->
        <div
            class="rank-number-section"
            :class="rankNumberSectionClass">
            <slot
                name="rankNumber"
                :rank="rank"
                :rank-icon="rankIcon"
                :is-empty="isEmpty">
                <div class="rank-number-content">
                    <img
                        v-if="rankIcon"
                        :src="rankIcon"
                        :alt="`第${rank}名`"
                        class="rank-icon"
                        :class="rankIconClass"
                    />
                </div>
            </slot>
        </div>

        <!-- 头像区域 -->
        <div
            class="avatar-section"
            :class="avatarSectionClass">
            <slot
                name="avatar"
                :user-info="userInfo"
                :is-empty="isEmpty">
                <div
                    class="avatar-container"
                    :class="avatarContainerClass"
                    @click.stop="handleAvatarClick">
                    <!-- 头像边框装饰 -->
                    <div
                        v-if="avatarBorderImage"
                        class="avatar-border"
                        :class="avatarBorderClass"
                        :style="{ backgroundImage: `url(${avatarBorderImage})` }">
                    </div>

                    <!-- 头像图片 -->
                    <img
                        :src="getAvatar(userInfo?.username)"
                        :alt="userInfo?.nickname || '头像'"
                        class="avatar-image"
                        :class="avatarImageClass"
                        @error="handleAvatarError"
                    />

                    <!-- 房间状态 -->
                    <div
                        v-if="showRoomStatus && channelStatus"
                        class="room-status"
                        :class="roomStatusClass"
                        @click.stop="handleRoomClick">
                        <slot
                            name="roomStatus"
                            :channel-status="channelStatus"
                            :channel-info="channelInfo">
                            <room-status
                                :status="channelStatus"
                                :cid="channelInfo?.channelId"
                            ></room-status>
                        </slot>
                    </div>
                </div>
            </slot>
        </div>

        <!-- 用户信息区域 -->
        <div
            class="user-info-section"
            :class="userInfoSectionClass">
            <slot
                name="userInfo"
                :user-info="userInfo"
                :nickname="nickname"
                :is-empty="isEmpty">
                <!-- 昵称背景 -->
                <div
                    class="nickname-container"
                    :class="nicknameContainerClass"
                    :style="nicknameBackgroundStyle">
                    <div
                        class="nickname"
                        :class="nicknameClass"
                        :title="userInfo?.nickname">
                        {{ nickname }}
                    </div>
                </div>

                <!-- 用户ID -->
                <div
                    v-if="showUserId && userId"
                    class="user-id"
                    :class="userIdClass">
                    ID: {{ userId }}
                </div>
            </slot>
        </div>

        <!-- 数值区域 -->
        <div
            class="value-section"
            :class="valueSectionClass">
            <slot
                name="value"
                :value="value"
                :value-label="valueLabel"
                :is-empty="isEmpty">
                <!-- 数值背景 -->
                <div
                    class="value-container"
                    :class="valueContainerClass"
                    :style="valueBackgroundStyle">
                    <div class="value-content">
                        <!-- 数值标签和数值 -->
                        <span
                            v-if="valueLabel"
                            class="value-label">{{ valueLabel }}</span>
                        <span class="value-number">{{ omitValue(value) }}收藏石</span>
                    </div>
                </div>
            </slot>
        </div>

        <!-- 额外装饰区域 -->
        <div
            v-if="$slots.decoration"
            class="decoration-section"
            :class="decorationSectionClass">
            <slot
                name="decoration"
                :rank="rank"
                :data="data"
                :is-empty="isEmpty" />
        </div>

        <!-- 额外内容区域 -->
        <div
            v-if="$slots.extra"
            class="extra-section"
            :class="extraSectionClass">
            <slot
                name="extra"
                :data="data"
                :rank="rank"
                :is-empty="isEmpty" />
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { getAvatar, omitValue, safeOmitTxt, toPerson, toRoom } from '@/utils/index';

const props = defineProps({
    // 排名数据
    data: {
        type: Object,
        required: true,
    },

    // 排名
    rank: {
        type: [Number, String],
        required: true,
        validator: value => value >= 1 && value <= 3,
    },

    // 是否为空项目
    isEmpty: {
        type: Boolean,
        default: false,
    },

    // 数值标签
    valueLabel: {
        type: String,
        default: '',
    },

    // 是否可点击
    clickable: {
        type: Boolean,
        default: true,
    },

    // 是否显示皇冠
    showCrown: {
        type: Boolean,
        default: true,
    },

    // 是否显示装饰背景
    showDecoration: {
        type: Boolean,
        default: true,
    },

    // 是否显示房间状态
    showRoomStatus: {
        type: Boolean,
        default: true,
    },

    // 是否显示用户ID
    showUserId: {
        type: Boolean,
        default: false,
    },

    // 主题样式
    theme: {
        type: String,
        default: 'default',
        validator: value => ['default', 'golden', 'colorful', 'elegant'].includes(value),
    },

    // 自定义图片资源
    customImages: {
        type: Object,
        default: () => ({}),
    },

    // 样式类
    containerClass: {
        type: [String, Array, Object],
        default: '',
    },

    containerStyle: {
        type: [String, Object],
        default: '',
    },
});

const emit = defineEmits([
    'click', // 点击TOP项
    'avatarClick', // 点击头像
    'roomClick', // 点击房间状态
]);

// 用户信息
const userInfo = computed(() => props.data?.userInfo || {});

// 头像
const avatar = computed(() => {
    return getAvatar(userInfo.value?.username || userInfo.value?.id);
});

// 昵称
const nickname = computed(() => {
    if (props.isEmpty)
        return '虚位以待';

    const name = userInfo.value?.nickname || '未知用户';

    return safeOmitTxt(name, 5);
});

// 用户ID
const userId = computed(() => userInfo.value?.alias || userInfo.value?.id || '');

// 数值
const value = computed(() => props.data?.value || 0);

// 房间状态
const channelStatus = computed(() => props.data?.channelInfo?.status || '');

// 频道信息
const channelInfo = computed(() => props.data?.channelInfo || {});

// 主题样式类
const themeClass = computed(() => `rank-top--${props.theme}`);

// 默认图片资源
const defaultImages = computed(() => {
    const baseImages = {
        1: {
            background: requireImg('tab1/<EMAIL>'),
            crown: '',
            avatarBorder: '',
            nicknameBackground: '',
            valueBackground: '',
            rankIcon: '',
        },
        2: {
            background: requireImg('tab1/<EMAIL>'),
            crown: '',
            avatarBorder: '',
            nicknameBackground: '',
            valueBackground: '',
            rankIcon: '',
        },
        3: {
            background: requireImg('tab1/<EMAIL>'),
            crown: '',
            avatarBorder: '',
            nicknameBackground: '',
            valueBackground: '',
            rankIcon: '',
        },
    };

    return baseImages[props.rank] || baseImages[1];
});

// 合并后的图片资源
const images = computed(() => ({
    ...defaultImages.value,
    ...props.customImages,
}));

// 各种图片资源
const backgroundImage = computed(() => images.value.background);
const crownImage = computed(() => images.value.crown);
const avatarBorderImage = computed(() => images.value.avatarBorder);
const nicknameBackgroundImage = computed(() => images.value.nicknameBackground);
const valueBackgroundImage = computed(() => images.value.valueBackground);
const rankIcon = computed(() => images.value.rankIcon);

// 背景样式
const nicknameBackgroundStyle = computed(() => ({
    backgroundImage: nicknameBackgroundImage.value ? `url(${nicknameBackgroundImage.value})` : '',
}));

const valueBackgroundStyle = computed(() => ({
    backgroundImage: valueBackgroundImage.value ? `url(${valueBackgroundImage.value})` : '',
}));

// 样式类计算
const backgroundClass = computed(() => `rank-background--${props.rank}`);
const crownClass = computed(() => `rank-crown--${props.rank}`);
const rankNumberSectionClass = computed(() => `rank-number-section--${props.rank}`);
const avatarSectionClass = computed(() => `avatar-section--${props.rank}`);
const userInfoSectionClass = computed(() => `user-info-section--${props.rank}`);
const valueSectionClass = computed(() => `value-section--${props.rank}`);

const rankIconClass = computed(() => `rank-icon--${props.rank}`);
const rankTextClass = computed(() => `rank-text--${props.rank}`);
const avatarContainerClass = computed(() => `avatar-container--${props.rank}`);
const avatarBorderClass = computed(() => `avatar-border--${props.rank}`);
const avatarImageClass = computed(() => `avatar-image--${props.rank}`);
const roomStatusClass = computed(() => `room-status--${props.rank}`);
const nicknameContainerClass = computed(() => `nickname-container--${props.rank}`);
const nicknameClass = computed(() => `nickname--${props.rank}`);
const userIdClass = computed(() => `user-id--${props.rank}`);
const valueContainerClass = computed(() => `value-container--${props.rank}`);

const decorationSectionClass = computed(() => `decoration-section--${props.rank}`);
const extraSectionClass = computed(() => `extra-section--${props.rank}`);

/**
 * 处理点击事件
 */
function handleClick() {
    if (props.clickable && !props.isEmpty) {
        emit('click', {
            data: props.data,
            rank: props.rank,
            isEmpty: props.isEmpty,
        });
    }
}

/**
 * 处理头像点击
 */
function handleAvatarClick() {
    if (!props.isEmpty && userInfo.value?.username) {
        emit('avatarClick', {
            username: userInfo.value.username,
            userInfo: userInfo.value,
        });

        toPerson(userInfo.value.username);
    }
}

/**
 * 处理房间点击
 */
function handleRoomClick() {
    if (channelInfo.value?.channelId) {
        emit('roomClick', {
            channelId: channelInfo.value.channelId,
            channelInfo: channelInfo.value,
        });

        toRoom(channelInfo.value.channelId);
    }
}

/**
 * 处理头像加载错误
 */
function handleAvatarError(event) {
    // 设置默认头像
    event.target.src = '/default-avatar.png';
}

// 对外暴露的方法
defineExpose({
    // 状态
    userInfo,
    nickname,
    value,
    images,

    // 方法
    handleClick,
    handleAvatarClick,
    handleRoomClick,
});
</script>

<style scoped lang="less">
.rank-top {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;

    &.is-empty {
        opacity: 0.6;

        .avatar-image {
            background: rgba(255, 255, 255, 0.1);
        }
    }
}

.rank-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    z-index: 1;
}

.rank-crown {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 25px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 3;
}

.rank-number-section {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.rank-number-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

.rank-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

.rank-text {
    font-size: 16px;
    font-weight: 700;
    color: #ffd700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.avatar-section {
    position: relative;
    margin-top: 40px;
    z-index: 2;
}

.avatar-container {
    position: relative;
    width: 56.5px;
    height: 56.5px;
    border: 2px solid #fbd689;
    border-radius: 50%;
}

.avatar-border {
    position: absolute;
    top: -5px;
    left: -5px;
    width: 90px;
    height: 90px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 1;
}

.avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    cursor: pointer;
    transition: transform 0.2s ease;
    z-index: 2;
}

.room-status {
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
}

.room-status-content {
    padding: 2px 6px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 10px;
    border-radius: 10px;
    white-space: nowrap;
}

.user-info-section {
    margin-top: 14px;
    z-index: 2;
}

.nickname-container {
    position: relative;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
}

.nickname {
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    color: #ffe3be;
    white-space: nowrap;
}

.user-id {
    margin-top: 2px;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
}

.value-section {
    margin-top: 4px;
    z-index: 2;
}

.value-container {
    position: relative;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.value-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.value-label {
    font-size: 14px;
    font-weight: 500;
    color: #ffec6f;
}

.value-number {
    font-size: 11px;
    font-weight: 500;
    color: #ffec6f;
    margin-top: 2px;
}

.decoration-section,
.extra-section {
    z-index: 2;
}

// 排名特定样式
.rank-1 {
    width: 185px;
    height: 206px;

    .rank-text {
    }
    .avatar-section--1 {
        margin-top: 47px;
    }

    .nickname {
    }

    .value-number {
    }
}

.rank-2 {
    width: 130px;
    height: 184px;
    .rank-text {
    }

    .nickname {
    }

    .value-number {
    }
}

.rank-3 {
    width: 130px;
    height: 184px;
    .rank-text {
    }

    .nickname {
    }

    .value-number {
    }
}

// 主题样式
.rank-top--golden {
    .nickname,
    .value-number {
    }
}

.rank-top--colorful {
    &.rank-1 {
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 165, 0, 0.1));
    }

    &.rank-2 {
        background: linear-gradient(135deg, rgba(192, 192, 192, 0.2), rgba(169, 169, 169, 0.1));
    }

    &.rank-3 {
        background: linear-gradient(135deg, rgba(205, 127, 50, 0.2), rgba(184, 134, 11, 0.1));
    }
}

.rank-top--elegant {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    .avatar-container {
        border: 3px solid rgba(255, 255, 255, 0.3);
    }
}

// 尺寸样式
.rank-top--small {
    .avatar-container {
        width: 60px;
        height: 60px;
    }

    .nickname {
        font-size: 11px;
    }

    .value-number {
        font-size: 12px;
    }
}

.rank-top--large {
    .avatar-container {
        width: 100px;
        height: 100px;
    }

    .nickname {
        font-size: 15px;
    }

    .value-number {
        font-size: 16px;
    }
}

// 响应式适配
@media (max-width: 375px) {
    .rank-top {
        transform: scale(0.9);
    }

    .rank-top--small {
        transform: scale(0.8);
    }
}
</style>
