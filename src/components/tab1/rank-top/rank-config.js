/**
 * common-components/rank-top/rank-config.js
 * TOP排名配置常量和工具函数
 */

/**
 * 默认图片资源配置
 */
export const DEFAULT_RANK_IMAGES = {
    // 第一名资源
    1: {
        background: '/rank-top/bg-top1.png',
        crown: '/rank-top/crown-gold.png',
        avatarBorder: '/rank-top/avatar-border-gold.png',
        nicknameBackground: '/rank-top/nickname-bg-gold.png',
        valueBackground: '/rank-top/value-bg-gold.png',
        rankIcon: '/rank-top/rank-icon-1.png',
        decoration: '/rank-top/decoration-gold.png',
    },
    // 第二名资源
    2: {
        background: '/rank-top/bg-top2.png',
        crown: '/rank-top/crown-silver.png',
        avatarBorder: '/rank-top/avatar-border-silver.png',
        nicknameBackground: '/rank-top/nickname-bg-silver.png',
        valueBackground: '/rank-top/value-bg-silver.png',
        rankIcon: '/rank-top/rank-icon-2.png',
        decoration: '/rank-top/decoration-silver.png',
    },
    // 第三名资源
    3: {
        background: '/rank-top/bg-top3.png',
        crown: '/rank-top/crown-bronze.png',
        avatarBorder: '/rank-top/avatar-border-bronze.png',
        nicknameBackground: '/rank-top/nickname-bg-bronze.png',
        valueBackground: '/rank-top/value-bg-bronze.png',
        rankIcon: '/rank-top/rank-icon-3.png',
        decoration: '/rank-top/decoration-bronze.png',
    },
};

/**
 * 图片资源样式集合
 */
export const RANK_IMAGE_STYLES = {
    // 皇冠样式
    CROWN_STYLE: {
        1: { background: '/rank-top/crown-gold.png', crown: '/rank-top/crown-gold.png' },
        2: { background: '/rank-top/crown-silver.png', crown: '/rank-top/crown-silver.png' },
        3: { background: '/rank-top/crown-bronze.png', crown: '/rank-top/crown-bronze.png' },
    },

    // 奖牌样式
    MEDAL_STYLE: {
        1: { background: '/rank-top/medal-gold.png', crown: '/rank-top/medal-gold.png' },
        2: { background: '/rank-top/medal-silver.png', crown: '/rank-top/medal-silver.png' },
        3: { background: '/rank-top/medal-bronze.png', crown: '/rank-top/medal-bronze.png' },
    },

    // 奖杯样式
    TROPHY_STYLE: {
        1: { background: '/rank-top/trophy-gold.png', crown: '/rank-top/trophy-gold.png' },
        2: { background: '/rank-top/trophy-silver.png', crown: '/rank-top/trophy-silver.png' },
        3: { background: '/rank-top/trophy-bronze.png', crown: '/rank-top/trophy-bronze.png' },
    },

    // 简约样式
    SIMPLE_STYLE: {
        1: { background: '', crown: '', rankIcon: '👑' },
        2: { background: '', crown: '', rankIcon: '🥈' },
        3: { background: '', crown: '', rankIcon: '🥉' },
    },
};

/**
 * TOP排名预设配置
 */
export const RANK_TOP_PRESETS = {
    // 默认配置
    DEFAULT: {
        showCrown: true,
        showDecoration: true,
        showRoomStatus: true,
        showUserId: false,
        clickable: true,
        imageStyle: 'CROWN_STYLE',
    },

    // 简洁配置
    SIMPLE: {
        showCrown: false,
        showDecoration: false,
        showRoomStatus: false,
        showUserId: false,
        clickable: false,
        imageStyle: 'SIMPLE_STYLE',
    },

};

/**
 * 排名颜色配置
 */
export const RANK_COLORS = {
    1: {
        primary: '#FFD700',
        secondary: '#FFA500',
        textShadow: '#ff7b8d',
        gradient: 'linear-gradient(135deg, #FFD700, #FFA500)',
    },
    2: {
        primary: '#C0C0C0',
        secondary: '#A0A0A0',
        textShadow: '#5f82ca',
        gradient: 'linear-gradient(135deg, #C0C0C0, #A0A0A0)',
    },
    3: {
        primary: '#CD7F32',
        secondary: '#B8860B',
        textShadow: '#e48b7d',
        gradient: 'linear-gradient(135deg, #CD7F32, #B8860B)',
    },
};

/**
 * 创建TOP排名配置
 * @param {object} options - 配置选项
 * @returns {object} 完整配置
 */
export function createRankTopConfig(options = {}) {
    const {
        preset = 'DEFAULT',
        theme,
        size,
        showCrown,
        showDecoration,
        showRoomStatus,
        showUserId,
        clickable,
        imageStyle,
        customImages = {},
        ...rest
    } = options;

    const baseConfig = RANK_TOP_PRESETS[preset] || RANK_TOP_PRESETS.DEFAULT;

    return {
        ...baseConfig,
        ...(theme && { theme }),
        ...(size && { size }),
        ...(typeof showCrown === 'boolean' && { showCrown }),
        ...(typeof showDecoration === 'boolean' && { showDecoration }),
        ...(typeof showRoomStatus === 'boolean' && { showRoomStatus }),
        ...(typeof showUserId === 'boolean' && { showUserId }),
        ...(typeof clickable === 'boolean' && { clickable }),
        ...(imageStyle && { imageStyle }),
        customImages,
        ...rest,
    };
}

/**
 * 获取排名图片资源
 * @param {number} rank - 排名
 * @param {string} style - 图片样式
 * @param {object} customImages - 自定义图片
 * @returns {object} 图片资源配置
 */
export function getRankImages(rank, style = 'CROWN_STYLE', customImages = {}) {
    const defaultImages = DEFAULT_RANK_IMAGES[rank] || DEFAULT_RANK_IMAGES[1];
    const styleImages = RANK_IMAGE_STYLES[style]?.[rank] || {};

    return {
        ...defaultImages,
        ...styleImages,
        ...customImages,
    };
}

/**
 * 标准化TOP排名数据
 * @param {object} data - 原始数据
 * @param {number} rank - 排名
 * @returns {object} 标准化后的数据
 */
export function normalizeRankTopData(data, rank) {
    if (!data)
        return null;

    return {
        rank,
        value: data.value || 0,
        userInfo: {
            id: data.userInfo?.id || data.id,
            username: data.userInfo?.username || data.username,
            nickname: data.userInfo?.nickname || data.nickname || '未知用户',
            avatar: data.userInfo?.avatar || data.avatar || '',
            alias: data.userInfo?.alias || data.alias || '',
            ...data.userInfo,
        },
        channelInfo: data.channelInfo || {},
        extraInfo: data.extraInfo || '',
        valueHuman: data.valueHuman || '',
        ...data,
    };
}

/**
 * 生成虚位以待TOP数据
 * @param {number} rank - 排名
 * @returns {object} 虚位以待数据
 */
export function generatePlaceholderTopData(rank) {
    return {
        rank,
        value: 0,
        isEmpty: true,
        userInfo: {
            id: `placeholder_top_${rank}`,
            username: '',
            nickname: '虚位以待',
            avatar: '',
            alias: '',
        },
        channelInfo: {},
        extraInfo: '',
        valueHuman: '0',
    };
}

/**
 * 活动TOP排名配置
 */
export const ACTIVITY_RANK_TOP_CONFIG = createRankTopConfig({
    preset: 'LUXURY',
    imageStyle: 'CROWN_STYLE',
});
/**
 * 简洁TOP排名配置
 */
export const SIMPLE_RANK_TOP_CONFIG = createRankTopConfig({
    preset: 'SIMPLE',
    imageStyle: 'SIMPLE_STYLE',
});
