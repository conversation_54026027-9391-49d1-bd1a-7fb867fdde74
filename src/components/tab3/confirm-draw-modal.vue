<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="dialog-wrapper">
            <div class="btns">
                <img
                    class="btn"
                    src="@/assets/img/tab3/<EMAIL>"
                    @click="onOk"

                />
                <img
                    class="btn"
                    src="@/assets/img/tab3/<EMAIL>"
                    @click="onCancel"
                />
            </div>
        </div>
    </modal-container>
</template>

<script setup>
import useDrawStore from './use-draw';

const store = useDrawStore();

const tickId = ref('');
const today = ref(0);
const isShow = ref(false);

useEventBus('confirm-draw-modal').on((params) => {
    isShow.value = params.show;
    tickId.value = params.tickId;
    today.value = params.today;
});
// 每日补卡限制 - 使用本地存储记录
const dailyRepairRecord = useActivityStorage('daily-repair-record', {});

const onOk = async () => {
    const result = await store.repair({ tickId: tickId.value });

    if (result && result.code === 0) {
        // 记录今天已经补卡
        dailyRepairRecord.value[today.value] = true;

        // 刷新数据
        await store.flashDrawInfo();
        await store.getRepairTickInfo();

        showToast('补卡任务已开启，开始送礼吧！');
    }
    else {
        showToast(result?.message || '补卡失败，请稍后重试');
    }

    // 刷新数据
    await store.flashDrawInfo();
    await store.getRepairTickInfo();
    isShow.value = false;
};

const onCancel = async () => {
    isShow.value = false;
};
</script>

<style lang="less" scoped>
.dialog-wrapper {
    position: relative;
    width: 346px;
    height: 277px;
    background-image: url('@/assets/img/tab3/<EMAIL>');
    background-size: 100% 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    .close-btn {
        position: absolute;
        top: 13.5px;
        right: 10.5px;
        width: 23px;
        height: 23px;
    }

    .btns {
        position: absolute;
        bottom: 34px;
        left: 0;
        width: 100%;
        display: flex;
        justify-content: center;

        .btn {
            width: 127.5px;
            height: 47px;

            &:first-child {
                margin-right: 47px;
            }
        }
    }
}
</style>
