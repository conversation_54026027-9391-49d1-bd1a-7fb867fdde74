<template>
    <div class="video-box">
        <div
            id="controls"
            class="controls">
            <div class="slider">
                <!-- 进度条容器 -->
                <div
                    id="control"
                    ref="control"
                    class="control"
                    @click="setProgress"
                    @touchmove="controlMove"
                    @touchend="controlEnd"
                >
                    <!-- 进度条本条 -->
                    <div
                        class="progress"
                        :style="{ width: `${progressWidth}px` }"
                    />
                    <!-- 滑块 -->
                    <div
                        v-if="progressWidth >= 0"
                        class="slider_circle"
                        :style="{ left: `${progressWidth - 20}px` }"
                        @touchstart="sliderStart"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';

const emit = defineEmits(['updateWidth']);
// 格式化时间函数

// video的ref
const homeVideo = ref(null);
// 展厅或者播放
const iconName = ref('play');
// 显示总时长
const duration = ref(0);
// 显示播放到的当前时间
const currentTime = ref(0);

const control = ref();

// 滑动到位置
const progressWidth = ref(0);
// 移动的类型，用来标明是否在拖动进度条
const moveType = ref('');
// 拖动开始时点击的位置
const moveOffsetX = ref(0);
// 拖动开始时进度条的宽度度
const curWidth = ref(0);

// 设置视频播放到指定的长度
const updateCurrentTime = (progressWidth, width) => {
    let dest = (progressWidth / width) * 100;
    emit('updateWidth', Math.floor(dest));
    localStorage.setItem('progressWidth', progressWidth);
};

// 设置进度条的长度
const setProgress = (e) => {
    e.preventDefault();
    const { left, width } = document.getElementById('control').getBoundingClientRect();
    progressWidth.value = e.clientX - left;
    console.log(left, e.clientX, 'left');

    updateCurrentTime(progressWidth.value, width);
};

onMounted(() => {
    if (localStorage.getItem('progressWidth')) {
        progressWidth.value = Number(localStorage.getItem('progressWidth'));
        const { width } = document.getElementById('control').getBoundingClientRect();
        updateCurrentTime(progressWidth.value, width);
    }
});

// 当开始触摸开始在圆点按下时
const sliderStart = (e) => {
    e.preventDefault();
    moveOffsetX.value = e.touches[0].clientX;
    moveType.value = 'slider';
    curWidth.value = progressWidth.value;
};

// 当触摸在controls上移动时
const controlMove = (e) => {
    if (moveType.value !== 'slider') {
        return false;
    }
    e.preventDefault();
    // 滑动距离可视区域左侧的距离
    const X = e.touches[0].clientX;
    // 得到本次拖动已经过的距离
    const cl = X - moveOffsetX.value;
    // 容器的宽度
    const { width } = document.getElementById('control').getBoundingClientRect();
    // 得到已拖动到宽度
    const ml = curWidth.value + cl;
    let proWidth;
    if (ml <= 0) {
        // 进度条长度最小和最大值的界定
        proWidth = 0;
    }
    else if (ml >= width) {
        proWidth = width;
    }
    else {
        proWidth = ml;
    }

    progressWidth.value = proWidth;
    // 更新当前时间
    updateCurrentTime(progressWidth.value, width);
};

// 滑动结束
const controlEnd = () => {
    moveType.value = '';
};

const dispControls = () => {
    let isDisp = document.getElementById('controls').style.visibility;
    if (isDisp === 'hidden') {
        document.getElementById('controls').style.visibility = 'visible';
    }
    else {
        document.getElementById('controls').style.visibility = 'hidden';
    }
};
</script>

<style lang="less" scoped>
.video-box {
    .controls {
        z-index: 1;
        display: flex;
        align-items: center;
        .play-btn {
            width: 30px;
            height: 30px;
            padding: 20px;
        }
        .progress {
            height: 18px;
            border-radius: 3px;
            background-image: url('/src/assets/img/<EMAIL>');
            background-size: cover;
        }
        .slider {
            flex: 1;
            .control {
                width: 173px;
                height: 18px;
                background-image: url('/src/assets/img/<EMAIL>');
                background-size: 100% 100%;
                position: relative;
                background-clip: content-box;
                z-index: 2;
            }
        }
        span {
            color: white;
            font-size: 24px;
            padding: 0 20px;
        }
        .slider_circle {
            position: absolute;
            left: 0;
            top: -2px;
            width: 44px;
            height: 42px;
            border-radius: 10px;
            border: 1px;
            background: url('/src/assets/img/<EMAIL>');
            background-size: 100% 100%;
        }
    }
}
</style>
