<template>
    <div class="reward-show">
        <div class="flex flex-col items-center">
            <div
                class="flex-center reward-box flex"
                :class="{ dark }"
            >
                <img
                    class="reward-img"
                    :src="getRewardInfo(rewardId)?.imageUrl"
                />
                <img
                    v-if="dark"
                    class="absolute right-[-10px] top-0 h-[18.5px] w-[35.5px]"
                    src="@/assets/img/tab3/<EMAIL>"
                    alt="">
                <div
                    v-if="!dark"
                    class="flex-center bg-default absolute right-[-8px] top-[0px] h-[16px] w-[33px] flex flex justify-center text-[12px] text-[#fff]"
                    :style="`background-image: url(${requireImg('tab3/<EMAIL>')})`"
                >
                    {{ num }}{{ getRewardInfo(rewardId)?.unit }}
                </div>
            </div>
            <p
                class="reward-name absolute top-[76px]"
                :class="{ 'shadow-text-gray': !dark }"
            >
                {{ getRewardInfo(rewardId)?.name }}
            </p>
            <p
                class="reward-name absolute top-[88px]"
                :class="{ 'shadow-text-gray': !dark }"
            >
                {{
                    getRewardInfo(rewardId)?.price
                        ? `${getRewardInfo(rewardId)?.price}豆`
                        : getRewardInfo(rewardId)?.mark
                }}
            </p>
            <slot></slot>
        </div>
    </div>
</template>

<script setup>
import { reactive, ref } from 'vue';

const props = defineProps({
    rewardId: {
        type: String,
        default: '',
    },
    dark: {
        type: Boolean,
        default: false,
    },
    num: {
        type: Number,
        default: 1,
    },
});
</script>

<style lang="less" scoped>
.reward-show {
    margin-bottom: auto;
    margin-right: 20px;

    .reward-box {
        position: relative;
        width: 73px;
        height: 74px;
        box-sizing: border-box;
        background-image: url('@/assets/img/tab3/<EMAIL>');
        background-size: 100% 100%;
        &.dark {
            background-image: url('@/assets/img/tab3/<EMAIL>');
            width: 78px;
            height: 73.5px;

            .reward-img {
                width: 80%;
                height: 80%;
                margin-top: 2px;
                object-fit: contain;
            }

            .reward-name {
                color: #fffde1;
            }
        }
    }

    .reward-img {
        width: 80%;
        height: 80%;
        margin: auto;
        object-fit: contain;
    }

    .reward-name {
        text-align: center;
        white-space: nowrap;
        width: 100%;
        font-size: 12px;
        font-weight: 400;
        color: #ffffff;
        line-height: 12px;
    }
}
.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
