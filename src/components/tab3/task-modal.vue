<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="dialog-wrapper pt-[180px]">
            <div class="text-center text-[12px] text-[#FFDBD3]">还差 <span class="text-[17px] text-[#FFC90F]">{{ omitValue(store.LINE - store.fixCardInfo?.sendValue) }}豆</span>补打{{ taskDayText }}卡，开启卷轴机会+1</div>

            <div
                class="relative mt-[21px] h-[19.5px] w-[307.5px] flex items-center border-[0.5px] border-[#ffc90f] border-[solid] rounded-[10.5px] bg-[#692804] bg-[#692804] px-[2px]"
            >
                <img
                    class="h-[12px] rounded-[6px] bg-[#ffeeac]"
                    :style="{
                        width: `${getProgressPercent(store.fixCardInfo.sendValue)}%`,
                    }"
                    alt="">
                <div class="text-shadow-gray absolute left-1/2 top-[5px] transform text-[11px] text-[#FFE5A8] leading-[10px] -translate-x-1/2">
                    已送出：{{ omitValue(store.fixCardInfo?.sendValue) }}豆
                </div>
                <img
                    class="absolute right-[-4px] top-[-12.5px] h-[44.5px] w-[44.5px]"
                    src="@/assets/img/tab3/<EMAIL>"
                    alt="">
            </div>
        </div>
    </modal-container>
</template>

<script setup>
import { computed, ref } from 'vue';
import useDrawStore from './use-draw';

const store = useDrawStore();
// 计算进度百分比
const getProgressPercent = (value) => {
    const target = 1000000; // 100万豆
    return Math.min((value / target) * 100, 100);
};

const isShow = ref(false);

const rounds = [
    { start: '2025-07-15 18:00:00', end: '2025-07-16 23:59:59', index: 0, dayText: '15日-16日' },
    { start: '2025-07-17 00:00:00', end: '2025-07-18 23:59:59', index: 1, dayText: '17日-18日' },
    { start: '2025-07-19 00:00:00', end: '2025-07-20 23:59:59', index: 2, dayText: '19日-20日' },
    { start: '2025-07-21 00:00:00', end: '2025-07-22 23:59:59', index: 3, dayText: '21日-22日' },
    { start: '2025-07-23 00:00:00', end: '2025-07-24 23:59:59', index: 4, dayText: '23日-24日' },
    { start: '2025-07-25 00:00:00', end: '2025-07-26 23:59:59', index: 5, dayText: '25日-26日' },
    { start: '2025-07-27 00:00:00', end: '2025-07-28 23:59:59', index: 6, dayText: '27日-28日' },
];
const index = ref(0);

const taskDayText = computed(() => rounds[index.value || 0].dayText);

useEventBus('task-modal').on(async (params) => {
    await store.getRepairTickInfo();
    isShow.value = params.show;
    index.value = params.index;
});
</script>

<style lang="less" scoped>
.dialog-wrapper {
    position: relative;
    width: 346px;
    height: 277px;
    background-image: url('@/assets/img/tab3/<EMAIL>');
    background-size: 100% 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    .close-btn {
        position: absolute;
        top: 13.5px;
        right: 10.5px;
        width: 23px;
        height: 23px;
    }

    .btns {
        position: absolute;
        bottom: 34px;
        left: 0;
        width: 100%;
        display: flex;
        justify-content: center;

        .btn {
            width: 127.5px;
            height: 47px;

            &:first-child {
                margin-right: 47px;
            }
        }
    }
}
</style>
