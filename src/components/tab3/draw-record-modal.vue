<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="dialog-wrapper">
            <div
                ref="wrapper"
                class="scroll-wrapper px-[4px]"
                @scroll="handleScroll"
            >
                <div
                    v-for="(item, index) of recordStore.list"
                    :key="index"
                    class="under-line relative w-[100%] flex flex-col px-[10px] py-[6px] text-[11px] text-[#fff]"
                >
                    <div
                        class="text-left"
                        v-html="dayjs.unix(item.time).tz().format('MM-DD HH:mm:ss')">
                    </div>
                    <div
                        class="text-left"
                        v-html="handleStr(item)">
                    </div>
                </div>
                <div
                    v-if="!recordStore.list?.length"
                    class="mt-[40px] w-[100%] text-center text-[14px] text-[#FFFFFF]"
                >
                    暂无记录
                </div>
            </div>
        </div>
    </modal-container>
</template>

<script setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import { throttle } from 'lodash-es';
import useDrawRecords from './use-draw-records';

const props = defineProps({
    type: {
        type: Number,
        default: 0,
    },
});
const wrapper = ref();

const recordStore = useDrawRecords();

const handleStr = (data) => {
    const text = `消耗开卷轴机会×1，获得:`;
    const str = data.rewards.map(item => `${getRewardInfo(item.id).name}${getPrice(item, item.num)}}`).join(',');

    return `${text}${str}`;
};

const handleScroll = () => {
    const scrollWrap = wrapper.value;
    if (scrollWrap.scrollTop + scrollWrap.clientHeight >= scrollWrap.scrollHeight - 30) {
        recordStore.nextPage();
    }
};

const isShow = ref(false);
useEventBus('draw-record-modal').on(({ show = true }) => {
    isShow.value = show;
    recordStore.reset();
    recordStore.nextPage();
});
</script>

<style lang="less" scoped>
.dialog-wrapper {
    position: relative;
    width: 314px;
    height: 252.5px;
    background-image: url('@/assets/img/tab3/<EMAIL>');
    background-size: 100% 100%;
    padding-top: 52px;
    box-sizing: border-box;

    .scroll-wrapper {
        width: 100%;
        height: 200px;
        margin: auto;
        box-sizing: border-box;
        overflow-y: auto;
        overflow-x: hidden;

        .record-item {
            width: 100%;
            font-size: 13px;
            font-weight: normal;
            text-align: left;
            color: #ff5f20;
            margin: auto;
            padding: 10px 0 14px 14px;
            display: flex;
            flex-direction: column;
        }
    }

    .btns {
        position: absolute;
        bottom: 25px;
        left: 0;
        width: 100%;
        display: flex;
        justify-content: center;

        .btn {
            width: 181px;
            height: 44px;

            &:first-child {
                margin-right: 20px;
            }
        }
    }
}

.under-line {
    // 奇数行
    &:nth-child(odd) {
        // background: #7d2a23;
    }
    // 偶数行
    &:nth-child(even) {
        background: #7d2a23;
    }
}
</style>
