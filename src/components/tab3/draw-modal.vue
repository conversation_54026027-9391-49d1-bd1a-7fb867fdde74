<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
        :lazy-render="false"
    >
        <div
            ref="containerRef"
            class="relative h-[100vh] w-[100vw]">
            <div
                v-show="HIGH_BG_LIST.includes(rewardList[0]?.id)"
                id="LottieGQ"
                class="absolute left-1/2 z-[-1] h-[100vh] w-[100vw] -translate-x-1/2 !transform"
            ></div>
            <div class="absolute left-1/2 top-1/2 -translate-[50%]">
                <div
                    class="low-bg main warp"
                    :class="[
                        HIGH_BG_LIST.includes(rewardList[0]?.id) && 'high-bg',
                    ]">
                    <div
                        class="max-h-[238px] w-[270px] flex overflow-x-auto"
                        :class="{
                            'justify-center': rewardList.length <= 3 }"
                    >
                        <div
                            v-for="(reward, index) of rewardList"
                            :key="`reward-${index}`"
                            class="reward">
                            <div
                                class="reward-frame relative"
                                :class="[
                                    HIGH_BG_LIST.includes(reward.id) && 'reward-frame-buff',
                                ]">
                                <img
                                    class="reward-img"
                                    :src="getRewardInfo(reward?.id).imageUrl" />
                                <div
                                    v-if="reward?.num"
                                    class="reward-value">
                                    <div
                                        class="reward-value-content stork-outside text-[#FFFFFF]"
                                    >
                                        {{ reward?.num }}{{ getRewardInfo(reward?.id).unit }}
                                    </div>
                                </div>
                            </div>
                            <div
                                class="mt-[2px] whitespace-nowrap text-center text-[17px] text-[#FFF9DB]"
                                v-html="
                                    getRewardName(reward, reward?.num)
                                "></div>
                        </div>
                    </div>
                </div>
                <div class="btns">
                    <img
                        class="btn"
                        :src="requireImg('tab3/<EMAIL>')"
                        @click="closeDialog" />
                </div>
            </div>
        </div>
    </modal-container>
</template>

<script setup>
import { reactive, ref } from 'vue';
import lottie from 'lottie-web';
import { rewardMap } from '@/config/reward';

const props = defineProps({
    type: {
        type: Number,
        default: 0,
    },
});

const HIGH_BG_LIST = [];

const rewardList = ref([]);
const isShow = ref(false);

const containerRef = ref();
const lottieAnimationGQ = ref();

useEventBus('draw-modal').on(async (params) => {
    isShow.value = params.show;
    // lottieAnimationGQ.value.play();
    rewardList.value = params.rewards;
});

function closeDialog() {
    isShow.value = false;
}

async function initLottie() {
    lottieAnimationGQ.value = lottie.loadAnimation({
        container: document.querySelector('#LottieGQ'),
        renderer: 'svg',
        loop: true,
        path: requirePublic('/result-modal__lucky.json'),
    });
}
const getRewardName = (reward, num = 1) => {
    const rewardInfo = getRewardInfo(reward?.id);
    return `${rewardInfo?.name}<br/>${getPrice(reward, reward.num)}`;
};

onMounted(() => {
    // initLottie();
});
</script>

<style lang="less" scoped>
.warp {
    position: relative;
    width: 374.5px;
    height: 489.5px;
    padding-top: 112px;
}

.big-warp {
    position: relative;
    // .full-bg('@/assets/img/<EMAIL>');
    width: 375px;
    height: 324.5px;
    padding-top: 104px;
}

.low-bg {
    background-size: 100% 100%;
    background-image: url('@/assets/img/tab3/<EMAIL>');
}

.high-bg {
    background-image: url('@/assets/img/tab3/<EMAIL>');
}

.main {
    position: relative;
    box-sizing: border-box;
    box-sizing: border-box;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .text-box {
        font-size: 12px;
        font-weight: 400;
        text-align: center;
        color: #d4cfc3;
    }

    .reward {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 10px;
    }

    .reward-frame {
        position: relative;
        overflow: hidden;
        width: 127px;
        height: 129px;
        background-image: url('@/assets/img/tab3/<EMAIL>');
        background-size: 100% 100%;
        box-sizing: border-box;
        padding: 7px;
        margin-right: 5px;
        margin-left: 5px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .reward-img {
            width: 80%;
            height: 80%;
            object-fit: contain;
        }
    }

    .reward-frame-buff {
        // background-image: url('@/assets/img/<EMAIL>') !important;
    }
}

.btns {
    position: absolute;
    bottom: 130px;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;

    .btn {
        width: 178px;
        height: 47px;
    }
}

.stork-outside {
    text-shadow:
        0 1px #e54620,
        1px 0 #e54620,
        -1px 0 #e54620,
        0 -1px #e54620;
}

.reward-value {
    position: absolute;
    top: 12px;
    right: 0;
    background-image: url('@/assets/img/tab3/<EMAIL>');
    background-size: 100% 100%;
    width: 44px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    color: #fff;
}
</style>
