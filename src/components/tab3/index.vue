<template>
    <div class="my-bg relative w-[100%] flex flex-col items-center overflow-hidden">
        <div
            ref="containerRef"
            class="bg-default relative h-[512px] w-[375px] flex flex-col items-center"
            :style="`background-image: url(${requireImg('tab3/<EMAIL>')})`">
            <reward
                class="jump absolute left-[34px] top-[75px] z-1"
                reward-id="ENT_C1"
                :num="3"
            >
            </reward>
            <reward
                class="jump absolute left-[148px] top-[58px] z-1"
                reward-id="PKG_C1"
                dark
            >
            </reward>
            <reward
                class="jump absolute left-[271px] top-[76px] z-1"
                reward-id="BDG_C1"
                :num="3"

            >
            </reward>
            <reward
                class="jump absolute left-[20px] top-[198px] z-1"
                reward-id="HD_C1"
                :num="3"

            >
            </reward>
            <reward
                class="jump absolute left-[287px] top-[198px] z-1"
                reward-id="HD_C1"
            >
            </reward>
            <reward
                class="jump absolute left-82 top-[298px] z-1"
                reward-id="ENT_C1"
            >
            </reward>
            <reward
                class="jump absolute left-[208px] top-[298px] z-1"
                reward-id="BDG_C1"
            >
            </reward>

            <div class="absolute top-[422px] flex items-center text-[12px] text-[#FFFFFF]">
                您当前还有 <span class="text-[#FFCD1F]">{{ store.tickValue }}</span>次 开启卷轴机会<img
                    class="mx-[2px] h-[14.5px] w-[14.5px]"
                    src="@/assets/img/tab3/<EMAIL>"
                    alt=""
                    @click="taskRef.scrollIntoView()">
            </div>

            <img
                class="absolute bottom-[18px] left-1/2 h-[60.5px] w-[200px] -translate-x-1/2"
                src="@/assets/img/tab3/<EMAIL>"
                @click="doDraw">
            <img
                class="absolute bottom-[92px] right-0 h-[24px] w-[70.5px]"
                src="@/assets/img/tab3/<EMAIL>"
                @click="useEventBus('draw-record-modal').emit({ show: true })">
        </div>

        <div
            ref="taskRef"
            class="bg-default relative mt-[-30px] h-[271px] w-[375px] flex flex-col items-center overflow-hidden px-[10px]"
            :style="`background-image: url(${requireImg('tab3/<EMAIL>')})`"
        >
            <div class="mt-[104px] h-[190px] w-[340px] overflow-x-auto">
                <gold-list></gold-list>
            </div>
        </div>

        <draw-modal></draw-modal>
    </div>
</template>

<script setup>
import useDraw from './use-draw';
import { stopDoubleClick } from '@/utils';

const taskRef = ref();
const store = useDraw();
const isLoading = ref(false);
const vm = ref();

async function doDraw() {
    try {
        if (!stopDoubleClick(vm, 2000))
            return;
        if (isLoading.value) {
            return;
        }
        if (store.isEnd) {
            showToast('活动已结束！');
            return;
        }
        if (!store.tickValue) {
            showToast('您暂无开启卷轴机会,快去完成任务解锁机会吧~');
            return;
        }

        isLoading.value = true;
        const { code, data } = await store.draw({ drawCount: 1 });
        if (code === 0) {
            let rewardList = [{ id: data.id, num: data.num }];
            useEventBus('draw-modal').emit({ show: true, rewards: rewardList });
            isLoading.value = false;
            store.init();
        }
    }
    catch (error) {
        isLoading.value = false;
    }
    finally {
        isLoading.value = false;
    }
}

const containerRef = ref();

onMounted(async () => {
    await store.init();
});
</script>

<style lang="less" scoped>
/*设置IOS页面长按不可复制粘贴，但是IOS上出现input、textarea不能输入，因此将使用-webkit-user-select:auto;*/
* {
    -webkit-touch-callout: none;
    /*系统默认菜单被禁用*/
    -webkit-user-select: none;
    /*webkit浏览器*/
    -khtml-user-select: none;
    /*早期浏览器*/
    -moz-user-select: none;
    /*火狐*/
    -ms-user-select: none;
    /*IE10*/
    user-select: none;
}

input,
textarea {
    -webkit-user-select: auto;
    /*webkit浏览器*/
    margin: 0px;
    padding: 0px;
    outline: none;
}

* {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -webkit-tap-highlight-color: transparent;
    /* For some Androids */
}

.btn2 {
    // pointer-events: none; //使元素成为鼠标事件的目标
}
.v-enter-active,
.v-leave-active {
    transition: opacity 0.5s ease;
}

.v-enter-from,
.v-leave-to {
    opacity: 0;
}
</style>
