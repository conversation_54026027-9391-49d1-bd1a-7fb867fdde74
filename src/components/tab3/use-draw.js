import dayjs from 'dayjs';
import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import useInitStore from '../../stores/modules/use-init-store';
import useLoading from '@/hooks/useLoading';
import { openTick as drawApi, getUserScroll as drawInfoApi, getRepairTick, repairTick } from '@/api/index';

const useDrawStore = defineStore('draw', () => {
    const broadcastsList = ref([]);
    const initStore = useInitStore();
    const taskList = ref([]);
    const tickValue = ref(0);

    const userDrawTimes = ref(0);

    const isLoading = ref(false);
    useLoading(isLoading);

    const fixCardInfo = ref({ sendValue: 0, fixCardDate: '' });

    const draw = async (req) => {
        try {
            const [{ code, data }] = await drawApi(req);
            return { code, data };
        }
        catch (e) {
            return false;
        }
    };

    const repair = async (req) => {
        try {
            const [{ code, data }] = await repairTick(req);
            return { code, data };
        }
        catch (e) {
            return false;
        }
    };

    const getRepairTickInfo = async () => {
        try {
            const [{ code, data }] = await getRepairTick();
            fixCardInfo.value = data?.fixCardInfo || {};
            return { code, data };
        }
        catch (e) {
            return false;
        }
    };
    const LINE = 1000000;
    const needRepairValue = computed(() => {
        return LINE - fixCardInfo.value?.sendValue;
    });

    const flashDrawInfo = async () => {
        isLoading.value = true;
        try {
            const [{ code, data }] = await drawInfoApi();
            tickValue.value = data?.tickValue || 0;
            taskList.value = data?.list || [];

            return { code, data };
        }
        finally {
            isLoading.value = false;
        }
    };

    const init = async () => {
        flashDrawInfo();
    };

    return {
        tickValue,
        taskList,
        broadcastsList,
        draw,
        flashDrawInfo,
        init,
        isEnd: dayjs
            .unix(initStore.serverTime)
            .isAfter(dayjs.unix(initStore.initData.endTime).add(1, 'day')),
        userDrawTimes,
        fixCardInfo,
        repair,
        needRepairValue,
        getRepairTickInfo,
        LINE,
    };
});

export default useDrawStore;
