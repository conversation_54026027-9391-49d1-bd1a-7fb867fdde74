<!-- src/components/tab3/gold-list.vue -->
<template>
    <div class="gold-list-container">
        <!-- 金卷轴列表 -->
        <div class="scroll-list ove-flow-x-auto w-[100%]">
            <div
                v-for="(item, index) in store.taskList"
                :key="item.tickId"
                class="scroll-item"
                :class="getScrollItemClass(item, index)"
                @click="showRepairModal(item, index)"
            >
                <img
                    :src="getStatusImgData(index).imageUrl"
                    :alt="`卷轴${index + 1}`"
                    class="h-[80%] w-[80%]"
                >

                <!-- 状态标识 -->
                <div
                    v-if="getStatusBadge(item, index)"
                    class="absolute left-1/2 top-0 h-[16.5px] w-[52px] -translate-x-1/2">
                    <img
                        class="h-full w-full"
                        :src="getStatusBadge(item, index)"
                        alt="状态">
                </div>

                <!-- 补卡按钮 -->

                <img
                    v-if="canShowRepairButton(item, index)"
                    class="absolute left-1/2 top-47 h-[17px] w-[62px] -translate-x-1/2"
                    src="@/assets/img/tab3/<EMAIL>"
                    alt="补卡"
                    @click="handleRepairCard(item, index)">

                <!-- 日期标签 -->
                <div class="absolute left-1/2 top-72 whitespace-nowrap text-[11px] text-[#FFF9DB] leading-[14px] -translate-x-1/2">
                    {{ getDateLabel(index) }}
                </div>
                <div class="absolute left-1/2 top-88 whitespace-nowrap text-[11px] text-[#FFF9DB] leading-[14px] -translate-x-1/2">
                    {{ getStatusText(item, index) }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import { round } from 'lodash';
import useDrawStore from './use-draw';
import useInitStore from '@/stores/modules/use-init-store';

const RWD_LIST = [
    {
        name: '自定义坐骑',
        id: 'ENT_C1',
        day: 1,
    },
    {
        name: '自定义麦位框',
        id: 'HD_C1',
        day: 1,
    },
    {
        name: '个人铭牌',
        id: 'BDG_C1',
        day: 1,
    },
    {
        name: '自定义麦位框',
        id: 'HD_C1',
        day: 3,
    },
    {
        name: '自定义坐骑',
        id: 'ENT_C1',
        day: 3,
    },
    {
        name: '个人铭牌',
        id: 'BDG_C1',
        day: 3,
    },
    {
        name: '30w豆包裹礼物',
        id: 'PKG_C1',
        day: 3,
    },
];

dayjs.extend(isBetween);

const initStore = useInitStore();
// 定义轮次时间
const rounds = [
    { start: '2025-07-15 18:00:00', end: '2025-07-16 23:59:59', index: 0, dayText: '15日-16日' },
    { start: '2025-07-17 00:00:00', end: '2025-07-18 23:59:59', index: 1, dayText: '17日-18日' },
    { start: '2025-07-19 00:00:00', end: '2025-07-20 23:59:59', index: 2, dayText: '19日-20日' },
    { start: '2025-07-21 00:00:00', end: '2025-07-22 23:59:59', index: 3, dayText: '21日-22日' },
    { start: '2025-07-23 00:00:00', end: '2025-07-24 23:59:59', index: 4, dayText: '23日-24日' },
    { start: '2025-07-25 00:00:00', end: '2025-07-26 23:59:59', index: 5, dayText: '25日-26日' },
    { start: '2025-07-27 00:00:00', end: '2025-07-28 23:59:59', index: 6, dayText: '27日-28日' },
];
// 获取当前轮次索引 (简化处理，实际应该根据活动开始时间计算)
const getCurrentRoundIndex = () => {
    const currentTime = dayjs.unix(initStore.serverTime).tz();

    // 查找当前时间所在轮次
    for (const round of rounds) {
        const startTime = dayjs(round.start, 'YYYY-MM-DD HH:mm:ss', 'Asia/Shanghai');
        const endTime = dayjs(round.end, 'YYYY-MM-DD HH:mm:ss', 'Asia/Shanghai');

        if (currentTime.isBetween(startTime, endTime, null, '[]')) {
            return round.index;
        }
    }

    // 如果当前时间早于活动开始时间，返回-1
    if (currentTime.isBefore(dayjs(rounds[0].start))) {
        return 0;
    }

    // 如果当前时间晚于活动结束时间，返回最后一轮索引
    if (currentTime.isAfter(dayjs(rounds[rounds.length - 1].end))) {
        return rounds.length - 1;
    }

    return 0;
};

const store = useDrawStore();

// 每日补卡限制 - 使用本地存储记录
const dailyRepairRecord = useActivityStorage('daily-repair-record', {});

// 获取当前日期字符串 (YYYY-MM-DD)
const getCurrentDateString = () => {
    return dayjs.unix(serverTime.value).format('YYYY-MM-DD');
};

// 检查今天是否已经补过卡
const hasTodayRepaired = computed(() => {
    const today = getCurrentDateString();
    return dailyRepairRecord.value[today] === true;
});

function showRepairModal(item, index) {
    if (item.isFixCard) {
        useEventBus('task-modal').emit({ show: true, ...item, index });
    }
}

// 获取卷轴项目样式类
const getScrollItemClass = (item, index) => {
    const classes = [];

    if (item.sendValue >= 1000000) {
        classes.push('completed');
    }

    if (item.isFixCard) {
        classes.push('repairing');
    }

    return classes.join(' ');
};

// 获取状态徽章图片
const getStatusBadge = (item, index) => {
    if (item.sendValue >= 1000000) {
        return requireImg('tab3/<EMAIL>'); // 成功解锁
    }
    else if (item.isFixCard) {
        return requireImg('tab3/<EMAIL>'); // 补卡中
    }
    else if (index === getCurrentRoundIndex()) {
        return requireImg('tab3/<EMAIL>'); // 进行中
    }
    else if (index < getCurrentRoundIndex()) {
        return requireImg('tab3/<EMAIL>'); // 已完成
    }
};

const getStatusImgData = (index) => {
    return { ...getRewardInfo(RWD_LIST[index].id), ...RWD_LIST[index] };
};

// 判断是否可以显示补卡按钮
const canShowRepairButton = (item, index) => {
    // 1. 该卷轴未完成任务 (sendValue < 100万豆)
    // 2. 没有东西在在补卡状态中
    // 3. 今天还没有补过卡
    // 4. 不是当前轮次的卷轴 (简化处理：前面的卷轴可以补卡)
    return item.sendValue < 1000000
           && !store.taskList.some(i => i.isFixCard)
           && !hasTodayRepaired.value
           && index < getCurrentRoundIndex();
};

// 获取日期标签
const getDateLabel = (index) => {
    return rounds[index].dayText;
};

// 获取状态徽章图片
const getStatusText = (item, index) => {
    if (index > getCurrentRoundIndex()) {
        return '送礼达到100w解锁';
    }
    if (item.sendValue >= 1000000) {
        return '获得开卷轴机会*1'; // 成功解锁
    }
    else if (item.isFixCard) {
        return `补卡进度${omitValue(item.sendValue)}/100w`; // 补卡中
    }
    else if (index === getCurrentRoundIndex()) {
        return `解锁进度${omitValue(item.sendValue)}/100w`; // 进行中
    }
    return '未获得开卷轴机会'; // 未完成
};

// 处理补卡操作
const handleRepairCard = async (item, index) => {
    try {
        // 检查是否已经补过卡
        if (hasTodayRepaired.value) {
            showToast('今天已经补过卡了，明天再来吧！');
            return;
        }
        useEventBus('confirm-draw-modal').emit({ show: true, ...item, today: index });
    }
    catch (error) {
        console.error('补卡操作失败:', error);
        showToast('补卡失败，请稍后重试');
    }
};
// scrollToCurrentDate
const scrollToCurrentDate = () => {
    const currentIndex = getCurrentRoundIndex();
    const scrollList = document.querySelector('.scroll-list');
    const scrollItem = document.querySelector(`.scroll-item:nth-child(${currentIndex + 1})`);
    // scrollItem?.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
};

// 初始化组件
onMounted(async () => {
    // 获取补卡信息
    await store.getRepairTickInfo();
    // 滚动到当前日期
    scrollToCurrentDate();
});
</script>

<style lang="less" scoped>
.gold-list-container {
    width: 100%;
    .scroll-list {
        align-items: center;
        display: flex;
        gap: 15px;

        .scroll-item {
            position: relative;
            display: flex;
            align-items: center;
            background-image: url('@/assets/img/tab3/<EMAIL>');
            background-size: 100% 100%;
            width: 67px;
            height: 68px;
            flex-shrink: 0;
            margin: 0 12px;
        }
    }
}
</style>
