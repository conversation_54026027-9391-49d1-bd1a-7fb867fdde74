// dom上的头像必须转成base64 import { getAvatarBase64 } from '@/utils/self';
// dom上的图片必须使用img标签，不能用background-image，不然会模糊
import { getDomBase64 } from '@/utils';

export default function useSaveDom() {
    const $el = ref(null);
    const downloading = ref(false);

    // return { success, base64 }
    async function download() {
        return new Promise((resolve) => {
            if ($el.value) {
                let base64 = '';
                downloading.value = true;
                setTimeout(async () => {
                    try {
                        base64 = await getDomBase64($el.value);
                        console.log('base64', base64);
                    }
                    catch (e) {
                        console.error(e);
                        base64 = '';
                    }
                    if (base64) {
                        const CALLBACK_FUNC_NAME = `$activitySaveDomCallback${Date.now()}`;
                        window[CALLBACK_FUNC_NAME] = (data) => {
                            let options = {};
                            try {
                                options = JSON.parse(data);
                            }
                            catch (err) {
                                console.error(err);
                                options = {};
                            }
                            downloading.value = false;
                            window[CALLBACK_FUNC_NAME] = undefined;
                            resolve({ base64, success: !!base64 && options.imgPath, imgPath: options.imgPath });
                        };
                        try {
                            TTJSBridge.invoke('operate', 'saveImgToGallery', JSON.stringify({ base64, imgType: 2 }), `window.${CALLBACK_FUNC_NAME}`);
                        }
                        catch (error) {
                            console.error(error);
                            downloading.value = false;
                        }
                    }
                    else {
                        downloading.value = false;
                        resolve({ base64: '', success: false });
                    }
                }, 200);
            }
            else {
                resolve({ base64: '', success: false });
            }
        });
    }

    return {
        $el,
        downloading,
        download,
    };
}
