let id = 1;

const nodes = new Set();
const ctx = '@@clickoutsideContext';

export const on = function (element, event, handler) {
    if (!(element && event && handler))
        return;
    if (document.addEventListener) {
        element.addEventListener(event, handler, false);
    }
    else {
        element.attachEvent(`on${event}`, handler);
    }
};

// 监听 document 点击事件
// todo 可以处理成多种事件的监听，比如 mouseon 等等
on(document, 'click', (e) => {
    const nodeList = [...nodes];
    nodeList.forEach(node => node[ctx].documentHandler(e));
});
on(document, 'touchstart', (e) => {
    const nodeList = [...nodes];
    nodeList.forEach(node => node[ctx].documentHandler(e));
});

// 创造一个document 点击处理
const createDocHandler = (el, handler) => {
    return function (e) {
        const target = e.target;
        if (!target || el.contains(target) || el === target)
            return;
        if (handler) {
            handler();
        }
    };
};

const isFunction = fn => typeof fn === 'function';

// 监听对应节点
export const add = (el, handler, vnode) => {
    if (!el || !isFunction(handler))
        return;
    nodes.add(el);
    el[ctx] = {
        id: id++,
        handler,
        documentHandler: createDocHandler(el, handler),
    };
};

export const update = (el, handler) => {
    el[ctx].handler = handler;
    el[ctx].documentHandler = createDocHandler(el, handler);
};

// 移除对应节点监听
export const remove = (el) => {
    if (!el)
        return;
    nodes.delete(el);
    delete el[ctx];
};
