import { onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { add, remove } from './clickoutside.js';

const noop = () => {};

const useClickOutside = (handleClickOutside = noop) => {
    const elRef = ref(null);

    onMounted(() => {
        add(elRef.value, handleClickOutside);
    });

    onBeforeUnmount(() => {
        remove(elRef.value);
    });

    watch(elRef, (el, prevEl) => {
        // 重新注册监听
        remove(prevEl);
        add(el, handleClickOutside);
    });

    return elRef;
};

export default useClickOutside;
