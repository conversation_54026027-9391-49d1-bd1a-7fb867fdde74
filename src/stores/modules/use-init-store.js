import { defineStore } from 'pinia';
import API from '@/api';

/**
 * @typedef {object} Init
 * @property {import('../../api/api.d.ts').InitResp} initData 初始化数据
 * @property {number} serverTime 服务器时间戳（秒）
 */

const useInitStore = defineStore('init', () => {
    /** @type {Init} */
    const stateInit = {
        initData: {},
        serverTime: 0,
    };
    // 业务数据
    const stateApi = {
        // ...
    };
    const state = reactive(Object.assign({}, stateInit, stateApi));

    const isEnd = computed(() => {
        const { endTime } = state.initData;
        return state.serverTime >= endTime;
    });

    let countTimeKey = null;
    // 模拟服务器时间
    function countServerTime(serverTime) {
        if (countTimeKey) {
            clearInterval(countTimeKey);
            countTimeKey = null;
        }
        window.serverTime = serverTime;
        state.serverTime = serverTime;
        countTimeKey = setInterval(() => {
            window.serverTime += 1;
            state.serverTime += 1;
        }, 1000);
    }

    /**
     * @name init 数据初始化
     * @type {function(import('../../api/api.d.ts').InitReq)}
     */
    const init = async (payload = {}, config = {}) => {
        const [{ code, data }] = await API.init(payload, config);
        const { cpPopup, rankPopup, cpBg, showCardLv, taskIndex } = myWebview.params;

        if (code === 0) {
            state.initData = data || {};
            countServerTime(data.serverTime);
        }
        if (cpPopup) {
            useEventBus('cp-reward-popup').emit({ show: true });
        }
        if (rankPopup) {
            useEventBus('rank-popup').emit({ show: true });
        }
        const { leftUsername, leftNickname, rightUsername, rightNickname, date, cardPopup } = myWebview.params;
        if (cpBg) {
            useEventBus('modal-cp-card').emit({
                show: true,
                leftUsername,
                leftNickname,
                rightUsername,
                rightNickname,
                date,
            });
        }
        if (cardPopup) {
            useEventBus('card-popup').emit({
                show: true,
            });
        }
        if (showCardLv) {
            useEventBus('dating-card-modal').emit({
                show: true,
                lv: Number(showCardLv),
            });
        }
        if (taskIndex) {
            useEventBus('task-modal').emit({
                show: true,
                index: Number(taskIndex),
            });
        }
        return { code };
    };

    return {
        ...toRefs(state),
        init,
        isEnd,
    };
});

export default useInitStore;
