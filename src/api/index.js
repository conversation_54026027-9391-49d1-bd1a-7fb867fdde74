// 执行pb.cjs命令后会覆盖掉手写的代码，请谨慎操作!
import request from '../utils/request';
import getMockData from './mockData';

export const fetchApi = ({ proPrefix = '/activity.Activity/', api, data = {}, config = {} }) => {
    const { mock } = myWebview.params;
    if (mock)
        return to(getMockData(api, data));

    const url = `${proPrefix}/${api}`.replace('//', '/');
    return to(request.post(url, data, config));
};

/**
 * ********************************
 * *********  活动接口 *************
 * ********************************
 */
const REQUEST_API_MAP = {
    init: 'init',
    getUserTask: 'getUserTask',
    getUserScroll: 'getUserScroll',
    openTick: 'openTick',
    repairTick: 'repairTick',
    getRepairTick: 'getRepairTick',
    getBaogeRewardRecord: 'getBaogeRewardRecord',
    getUserGift: 'getUserGift',
    getHighlightMark: 'getHighlightMark',
    getBlackGoldRank: 'getBlackGoldRank',
    getSupremeRank: 'getSupremeRank',
};

/** @type {function(import('./api.d.ts').InitReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').InitResp},any]>} */
export const init = (data, config) => fetchApi({ api: REQUEST_API_MAP.init, data, config });

/** @type {function(import('./api.d.ts').InitReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetUserTaskResp},any]>} */
export const getUserTask = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getUserTask, data, config });

/** @type {function(import('./api.d.ts').InitReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').getUserScrollResp},any]>} */
export const getUserScroll = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getUserScroll, data, config });

/** @type {function(import('./api.d.ts').InitReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').OpenTickResp},any]>} */
export const openTick = (data, config) => fetchApi({ api: REQUEST_API_MAP.openTick, data, config });

/** @type {function(import('./api.d.ts').RepairTickReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').Empty},any]>} */
export const repairTick = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.repairTick, data, config });

/** @type {function(import('./api.d.ts').GetRepairTickReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetRepairTickResp},any]>} */
export const getRepairTick = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getRepairTick, data, config });

/** @type {function(import('./api.d.ts').GetBaogeRewardRecordReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetBaogeRewardRecordResp},any]>} */
export const getBaogeRewardRecord = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getBaogeRewardRecord, data, config });

/** @type {function(import('./api.d.ts').InitReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetUserGiftResp},any]>} */
export const getUserGift = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getUserGift, data, config });

/** @type {function(import('./api.d.ts').GetHighlightMarkReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetHighlightMarkResp},any]>} */
export const getHighlightMark = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getHighlightMark, data, config });

/** @type {function(import('./api.d.ts').GetBlackGoldRankReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetBlackGoldRankResp},any]>} */
export const getBlackGoldRank = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getBlackGoldRank, data, config });

/** @type {function(import('./api.d.ts').GetSupremeRankReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetSupremeRankResp},any]>} */
export const getSupremeRank = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getSupremeRank, data, config });

export default {
    init,
    getUserTask,
    getUserScroll,
    openTick,
    repairTick,
    getRepairTick,
    getBaogeRewardRecord,
    getUserGift,
    getHighlightMark,
    getBlackGoldRank,
    getSupremeRank,
};
